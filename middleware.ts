import * as jose from "jose";
import { NextRequest, NextResponse } from "next/server";

const JWKS = jose.createRemoteJWKSet(
  new URL(process.env.KINDE_ISSUER_URL + "/.well-known/jwks"),
);

export default async function middleware(req: NextRequest) {
  // Allow auth requests
  const path = req.nextUrl.pathname;
  if (
    path.startsWith("/api/auth") ||
    path === "/api/wardrobe/cron" ||
    path.startsWith("/share") ||
    path.startsWith("/api/share")
  ) {
    return NextResponse.next();
  }

  // get token from cookie
  let token = req.cookies.get("access_token")?.value;
  if (!token) {
    // get token from header
    token = req.headers.get("Authorization")?.replace("Bearer ", "");
  }
  if (!token) {
    // rerturn 401 if no token
    return NextResponse.json({
      error: "Unauthorized",
      message: "No token provided",
    }, {
      status: 401,
    });
  }

  // verify token
  try {
    const { payload } = await jose.jwtVerify(token, JWKS);
    return NextResponse.next({
      headers: {
        ...req.headers,
        user_id: payload?.sub || "",
      },
    });
  } catch (error) {
    return NextResponse.json({
      error: "Unauthorized",
      message: (error as Error).message,
    }, {
      status: 401,
    });
  }
}

export const config = {
  matcher: ["/api/:path*"],
};
