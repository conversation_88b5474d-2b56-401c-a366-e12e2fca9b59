# stylenow.ai

https://stylenow.ai

## 1. Structure

### Database

* Type: Postgres Database
* Provider: Suggest purchase from [Supabase](https://supabase.com/pricing), The team plan **$599/mo** meets **SOC2 Compliance**
* Dump SQL: Will be provided after you setup your database

### Storage

* Type: Object Storage
* Provider: AWS S3, pay-as-you-go
* Images: Will be provided after you setup your S3 account

### Web&API

* Type: Next.js Project
* Provider: Suggest [Vercel](https://vercel.com/pricing), Pro subscription is enough **$20/mo/user**
* Code: deploy this repo to vercel

### OAuth

* Provider: [Kinde](https://kinde.com/pricing), Pro subscription is enough **$25/mon**
* Secret-Key: grab it after you create your own account

## 2. Deployment

### Environment Variables

#### `OPENAI_API_KEY`

- Prod
  - stylenow.ai: `ZNjZmkQUbvFWRhL.CQaNvDZtEXmYzgUpcieJdnQxGWCaOl`
  - valentino: `nsbvmjHaQlLCVWC.nYwftunVZHGjGXxVtRPqOdsoMnGvYa`

#### `BASE_URL`

`https://api.vivgrid.com/v1`

## Requirements

NodeJS >= 20

## 3. Local Development

```shell
# 1. install nodejs and yarn first
# 2. config local env vars in `.env.local`
# 3. run
yarn install
yarn dev
```
