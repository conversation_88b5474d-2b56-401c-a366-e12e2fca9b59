
# Your openai api key. (required)
OPENAI_API_KEY=sk-xxxx

# Access passsword, separated by comma. (optional)
CODE=your-password

# (optional) Use mock API for testing
MOCK=true

# You can start service behind a proxy
PROXY_URL=http://localhost:7890

# (optional)
# Default: Empty
# Googel Gemini Pro API key, set if you want to use Google Gemini Pro API.
GOOGLE_API_KEY=

# (optional)
# Default: https://generativelanguage.googleapis.com/
# Googel Gemini Pro API url without pathname, set if you want to customize Google Gemini Pro API url.
GOOGLE_URL=

# Override openai api request base url. (optional)
# Default: https://api.openai.com
# Examples: http://your-openai-proxy.com
BASE_URL=

# Specify OpenAI organization ID.(optional)
# Default: Empty
OPENAI_ORG_ID=

# (optional)
# Default: Empty
# If you do not want users to use GPT-4, set this value to 1.
DISABLE_GPT4=

# (optional)
# Default: Empty
# If you do not want users to input their own API key, set this value to 1.
HIDE_USER_API_KEY=

# (optional)
# Default: Empty
# If you do want users to query balance, set this value to 1.
ENABLE_BALANCE_QUERY=

# (optional)
# Default: Empty
# If you want to disable parse settings from url, set this value to 1.
DISABLE_FAST_LINK=

KINDE_CLIENT_ID=ccc7ecae88e84d7fa7e09575480e59a9
KINDE_CLIENT_SECRET=DrWVT2c2k3h3Gm0U0lLupO5sOsqHx15oGH9pCLZsySiYsz7H5Yml
KINDE_ISSUER_URL=https://stylenow.kinde.com
KINDE_SITE_URL=http://localhost:3000
KINDE_POST_LOGOUT_REDIRECT_URL=http://localhost:3000
KINDE_POST_LOGIN_REDIRECT_URL=http://localhost:3000

# The token for reading and writing to S3 bucket
AWS_REGION=us-west-1
AWS_KEY_ID=
AWS_ACCESS_SECRET_KEY=
AWS_BUCKET_NAME=stylenow.ai

# Postgres
POSTGRES_URL=
POSTGRES_URL_NON_POOLING=
POSTGRES_URL_NO_SSL=
POSTGRES_PRISMA_URL=
POSTGRES_USER=
POSTGRES_PASSWORD=
POSTGRES_HOST=
POSTGRES_DATABASE=

# Upstash
UPSTASH_URL=
UPSTASH_TOKEN=