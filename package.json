{"name": "stylenow", "private": false, "license": "mit", "scripts": {"dev": "next dev", "build": "cross-env BUILD_MODE=standalone next build", "postinstall": "prisma generate", "start": "next start", "lint": "next lint", "export": "cross-env BUILD_MODE=export BUILD_APP=1 next build", "export:dev": "cross-env BUILD_MODE=export BUILD_APP=1 next dev", "app:dev": "yarn tauri dev", "app:build": "yarn tauri build", "prompts": "node ./scripts/fetch-prompts.mjs", "prepare": "husky install", "proxy-dev": "sh ./scripts/init-proxy.sh && proxychains -f ./scripts/proxychains.conf yarn dev"}, "dependencies": {"@aws-sdk/client-s3": "^3.666.0", "@aws-sdk/lib-storage": "^3.667.0", "@fortaine/fetch-event-source": "^3.0.6", "@hello-pangea/dnd": "^16.5.0", "@kinde-oss/kinde-auth-nextjs": "^2.1.15", "@next/third-parties": "^14.1.0", "@prisma/client": "^5.10.2", "@rooks/use-window-size": "^4.11.2", "@svgr/webpack": "^6.5.1", "@vercel/analytics": "^0.1.11", "@vercel/speed-insights": "^1.0.2", "emoji-picker-react": "^4.5.15", "fuse.js": "^7.0.0", "html-to-image": "^1.11.11", "html2canvas": "^1.4.1", "jose": "^5.6.3", "marked": "^13.0.3", "mermaid": "^10.6.1", "nanoid": "^5.0.3", "next": "^13.4.9", "node-fetch": "^3.3.1", "prisma": "^5.10.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "react-router-dom": "^6.15.0", "react-zmage": "^0.8.5-beta.37", "rehype-highlight": "^6.0.0", "rehype-katex": "^6.0.3", "rehype-raw": "5.1.0", "remark-breaks": "^3.0.2", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "sass": "^1.59.2", "spark-md5": "^3.0.2", "swiper": "^11.0.7", "use-debounce": "^9.0.4", "zustand": "^4.3.8"}, "devDependencies": {"@tauri-apps/cli": "1.5.7", "@types/node": "^20.9.0", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.7", "@types/react-katex": "^3.0.0", "@types/spark-md5": "^3.0.4", "cross-env": "^7.0.3", "eslint": "^8.49.0", "eslint-config-next": "13.4.19", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.0", "lint-staged": "^13.2.2", "prettier": "^3.0.2", "typescript": "5.2.2", "webpack": "^5.88.1"}, "resolutions": {"lint-staged/yaml": "^2.2.2"}}