
# Your openai api key. (required)
# OPENAI_API_KEY=***************************************************
# OPENAI_API_KEY=***************************************************
OPENAI_API_KEY=lUASTApshIkolhO|fSmBiFALFIuivuUjEzWEHzPoxEFwNQ

# Access passsword, separated by comma. (optional)
CODE=

# (optional) Use mock API for testing
MOCK=false

# You can start service behind a proxy
PROXY_URL=

# (optional)
# Default: Empty
# Googel Gemini Pro API key, set if you want to use Google Gemini Pro API.
GOOGLE_API_KEY=

# (optional)
# Default: https://generativelanguage.googleapis.com/
# Googel Gemini Pro API url without pathname, set if you want to customize Google Gemini Pro API url.
GOOGLE_URL=

# Override openai api request base url. (optional)
# Default: https://api.openai.com
# Examples: http://your-openai-proxy.com
# BASE_URL=https://api.openai-proxy.com/v1/
BASE_URL=https://openai.vivgrid.com/v1/

# Specify OpenAI organization ID.(optional)
# Default: Empty
OPENAI_ORG_ID=

# (optional)
# Default: Empty
# If you do not want users to use GPT-4, set this value to 1.
DISABLE_GPT4=

# (optional)
# Default: Empty
# If you do not want users to input their own API key, set this value to 1.
HIDE_USER_API_KEY=

# (optional)
# Default: Empty
# If you do want users to query balance, set this value to 1.
ENABLE_BALANCE_QUERY=

# (optional)
# Default: Empty
# If you want to disable parse settings from url, set this value to 1.
DISABLE_FAST_LINK=

KINDE_CLIENT_ID=fb8740c024114820849e7a0110b1f6a4
KINDE_CLIENT_SECRET=vxESOer5FQmFMUaKl8QvufALazHIefQzUB7j0lm9606PbhqyTu
KINDE_ISSUER_URL=https://valentino.kinde.com
KINDE_SITE_URL=http://localhost:3000
KINDE_POST_LOGOUT_REDIRECT_URL=http://localhost:3000
KINDE_POST_LOGIN_REDIRECT_URL=http://localhost:3000

# The token for reading and writing to S3 bucket
AWS_REGION=us-west-1
AWS_KEY_ID=********************
AWS_ACCESS_SECRET_KEY=8PatHIuquLHt6UXQrnj7PRTrVJtAnXdy7xrMf8O1
AWS_BUCKET_NAME=stylenow-valentino

# Postgres
POSTGRES_URL="***************************************************************************************"
POSTGRES_URL_NON_POOLING="***************************************************************************************"
POSTGRES_URL_NO_SSL="***********************************************************************"
POSTGRES_PRISMA_URL="*************************************************************************************************************************"
POSTGRES_USER="ycloud"
POSTGRES_PASSWORD="L-T635pDgF5dwn8"
POSTGRES_HOST="*************:5432"
POSTGRES_DATABASE="stylenow-valentino"

# Upstash
UPSTASH_URL=https://genuine-rat-45099.upstash.io
UPSTASH_TOKEN=AbArASQgMTkxZTBkOTMtZDhlMS00Y2E4LWJiNDQtNTcwNjVlNWZhMDZhMzhiNzUxNGQ5Mzg0NDhjN2I1YjMxZmQ0OWQ0ZGZmNjU=