generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  // Uses connection pooling
  url = env("POSTGRES_PRISMA_URL")
  // Uses direct connection, ⚠️ make sure to keep this to `POSTGRES_URL_NON_POOLING`
  // or you'll have dangling databases from migrations
  directUrl = env("POSTGRES_URL_NON_POOLING")
}

model Wardrobe {
  id        String      @id
  userId    String
  imageName String
  imageUrl  String
  downloadUrl  String
  description String?    @default("")
  createdAt DateTime    @default(now())
  deleted   <PERSON>olean     @default(false)

  @@index(fields: [userId])
  @@index(fields: [userId, deleted])
}

model Dialogue {
  id       String @id
  message  Json?
  userId    String?
  createdAt DateTime    @default(now())
}
