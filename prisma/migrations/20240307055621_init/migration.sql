-- CreateTable
CREATE TABLE "Wardrobe" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "imageName" TEXT NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "downloadUrl" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Wardrobe_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Wardrobe_userId_idx" ON "Wardrobe"("userId");

-- CreateIndex
CREATE INDEX "Wardrobe_userId_deleted_idx" ON "Wardrobe"("userId", "deleted");
