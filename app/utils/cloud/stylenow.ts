import { SyncStore } from "@/app/store/sync";

export type StyleNowClient = ReturnType<typeof createStyleNowClient>;

export function createStyleNowClient(store: SyncStore) {
  return {
    async check() {
      try {
        const res = await fetch(this.path(""), {
          method: "GET",
        });
        return [200].includes(res.status);
      } catch (e) {
        console.error("[Upsta<PERSON>] failed to check", e);
      }
      return false;
    },

    async get() {
      const res = await fetch(this.path(""), {
        method: "GET",
      });

      const data = await res.json();
      return data.data;
    },

    async set(_: string, value: string) {
      await fetch(this.path(""), {
        method: "POST",
        body: JSON.stringify({ data: value }),
      });
    },

    path(path: string) {
      let url = "/api/sync";

      return url + path;
    },
  };
}
