import "katex/dist/katex.min.css";
import mermaid from "mermaid";
import React, {
  RefObject,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import ReactMarkdown from "react-markdown";
import Rehype<PERSON><PERSON>light from "rehype-highlight";
import RehypeKatex from "rehype-katex";
import rehypeRaw from "rehype-raw";
import RemarkBreaks from "remark-breaks";
import RemarkGfm from "remark-gfm";
import RemarkMath from "remark-math";
import { useDebouncedCallback } from "use-debounce";
import Star from "../icons/star.svg";
import Starred from "../icons/starred.svg";
import LoadingIcon from "../icons/three-dots.svg";
import { copyToClipboard } from "../utils";
import { ImageModal, showImageModal, showToast } from "./ui-lib";

export function Mermaid(props: { code: string }) {
  const ref = useRef<HTMLDivElement>(null);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    if (props.code && ref.current) {
      mermaid
        .run({
          nodes: [ref.current],
          suppressErrors: true,
        })
        .catch((e) => {
          setHasError(true);
          console.error("[Mermaid] ", e.message);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.code]);

  function viewSvgInNewWindow() {
    const svg = ref.current?.querySelector("svg");
    if (!svg) return;
    const text = new XMLSerializer().serializeToString(svg);
    const blob = new Blob([text], { type: "image/svg+xml" });
    showImageModal(URL.createObjectURL(blob));
  }

  if (hasError) {
    return null;
  }

  return (
    <div
      className="no-dark mermaid"
      style={{
        cursor: "pointer",
        overflow: "auto",
      }}
      ref={ref}
      onClick={() => viewSvgInNewWindow()}
    >
      {props.code}
    </div>
  );
}

export function PreCode(props: { children: any }) {
  const ref = useRef<HTMLPreElement>(null);
  const refText = ref.current?.innerText;
  const [mermaidCode, setMermaidCode] = useState("");

  const renderMermaid = useDebouncedCallback(() => {
    if (!ref.current) return;
    const mermaidDom = ref.current.querySelector("code.language-mermaid");
    if (mermaidDom) {
      setMermaidCode((mermaidDom as HTMLElement).innerText);
    }
  }, 600);

  useEffect(() => {
    setTimeout(renderMermaid, 1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refText]);

  return (
    <>
      {mermaidCode.length > 0 && (
        <Mermaid code={mermaidCode} key={mermaidCode} />
      )}
      <pre ref={ref}>
        <span
          className="copy-code-button"
          onClick={() => {
            if (ref.current) {
              const code = ref.current.innerText;
              copyToClipboard(code);
            }
          }}
        ></span>
        {props.children}
      </pre>
    </>
  );
}

function escapeDollarNumber(text: string) {
  let escapedText = "";

  for (let i = 0; i < text.length; i += 1) {
    let char = text[i];
    const nextChar = text[i + 1] || " ";

    if (char === "$" && nextChar >= "0" && nextChar <= "9") {
      char = "\\$";
    }

    escapedText += char;
  }

  return escapedText;
}

//图片放大缩小查看
export function ImageMessageModal(props: { onClose: () => void; data: any }) {
  const image = props.data;
  const metastring = image.properties.alt;
  const alt = metastring?.replace(/ *\{[^)]*\} */g, "");

  const emptyImage = {
    createdAt: "",
    deleted: true,
    description: null,
    downloadUrl: "",
    id: "",
    imageName: "",
    imageUrl: "",
    userId: "",
  };

  const [isLoading, setIsLoading] = useState(false);
  const [currentImage, setCurrentImage] = useState(emptyImage);

  const uploadToWardrobe = useCallback(async () => {
    setIsLoading(true);
    try {
      const data = {
        imageName: image.properties.src.slice(-36),
        imageUrl: image.properties.src,
        downloadUrl: image.properties.src,
      };

      const response = await fetch("/api/wardrobe", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      if (response.ok) {
        const currentImage = await response.json();
        setCurrentImage(currentImage);
        setIsLoading(false);
        showToast("Success");
      }
    } catch (error) {
      setIsLoading(false);
      showToast("Error: " + error);
      console.error("Error uploading image to wardrobe:", error);
    }
  }, [image.properties.src]);

  const deleteFromWardrobe = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/wardrobe", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ id: currentImage.id }),
      });
      if (response.ok) {
        setIsLoading(false);
        showToast("Success");
      }
    } catch (error) {
      showToast("Error");
      console.error("Error deleting image from wardrobe:", error);
      setIsLoading(false);
    }

    setCurrentImage(emptyImage);
  }, [currentImage.id]);

  useEffect(() => {
    (async () => {
      setIsLoading(true);
      try {
        const response = await fetch(
          `/api/wardrobe?imageUrl=${image.properties.src}`,
        );
        if (response.ok) {
          const { data } = await response.json();
          if (data) setCurrentImage(data);
          setIsLoading(false);
        }
      } catch (error) {
        showToast("Error");
        console.error("Error checking if image is starred in wardrobe:", error);
        setIsLoading(false);
      }
    })();
  }, [image.properties.src]);

  const StarButton = () => {
    const handleStarClick = () => {
      if (currentImage.deleted) {
        uploadToWardrobe();
      } else {
        deleteFromWardrobe();
      }
    };

    return isLoading ? (
      <LoadingIcon />
    ) : (
      <div onClick={handleStarClick}>
        {currentImage.deleted ? <Star /> : <Starred />}
      </div>
    );
  };

  return (
    <div className="modal-mask-img">
      <ImageModal
        url={image.properties.src}
        onClose={props.onClose}
        actions={[<StarButton key={"star"} />]}
      >
        <div style={{ padding: 60 }}>
          <img src={image.properties.src} className="postImg" alt={alt} />
        </div>
      </ImageModal>
    </div>
  );
}

function _postImgWrapper(props: any) {
  const [isImageShow, setIsImageShow] = useState(false);
  const image = props.data.node.children[0];
  const metastring = image.properties.alt;
  const alt = metastring?.replace(/ *\{[^)]*\} */g, "");
  const metaWidth = metastring.match(/{([^}]+)x/);
  const metaHeight = metastring.match(/x([^}]+)}/);
  const width = metaWidth ? metaWidth[1] : "768";
  const height = metaHeight ? metaHeight[1] : "432";
  const isPriority = metastring?.toLowerCase().match("{priority}");
  const hasCaption = metastring?.toLowerCase().includes("{caption:");
  const caption = metastring?.match(/{caption: (.*?)}/)?.pop();
  return (
    <div className="postImgWrapper">
      <img
        src={image.properties.src}
        className="postImg"
        alt={alt}
        // priority={isPriority}
        onClick={() => {
          setIsImageShow(true);
        }}
      />
      {hasCaption ? (
        <div className="caption" aria-label={caption}>
          {caption}
        </div>
      ) : null}
      {isImageShow && (
        <ImageMessageModal
          onClose={() => {
            setIsImageShow(false);
          }}
          data={image}
        />
      )}
    </div>
  );
}

function _MarkDownContent(props: { content: string }) {
  // const [isImageShow, setIsImageShow] = useState(false);
  const escapedContent = useMemo(
    () => escapeDollarNumber(props.content),
    [props.content],
  );

  return (
    <ReactMarkdown
      remarkPlugins={[RemarkMath, RemarkGfm, RemarkBreaks]}
      rehypePlugins={[
        rehypeRaw,
        RehypeKatex,
        [
          RehypeHighlight,
          {
            detect: false,
            ignoreMissing: true,
          },
        ],
      ]}
      components={{
        pre: PreCode,
        p: (pProps: any) => {
          // const { node } = pProps;
          if (pProps.node.children[0].tagName === "img") {
            return <_postImgWrapper data={pProps} />;
          }
          return <p>{pProps.children}</p>;
        },
        a: (aProps) => {
          const href = aProps.href || "";
          const isInternal = /^\/#/i.test(href);
          const target = isInternal ? "_self" : aProps.target ?? "_blank";
          return <a {...aProps} target={target} />;
        },
      }}
    >
      {escapedContent}
    </ReactMarkdown>
  );
}

export const MarkdownContent = React.memo(_MarkDownContent);

export function Markdown(
  props: {
    content: string;
    loading?: boolean;
    fontSize?: number;
    parentRef?: RefObject<HTMLDivElement>;
    defaultShow?: boolean;
  } & React.DOMAttributes<HTMLDivElement>,
) {
  const mdRef = useRef<HTMLDivElement>(null);

  return (
    <div
      className="markdown-body"
      style={{
        fontSize: `${props.fontSize ?? 14}px`,
      }}
      ref={mdRef}
      onContextMenu={props.onContextMenu}
      onDoubleClickCapture={props.onDoubleClickCapture}
      dir="auto"
    >
      {props.loading || props.content == "" ? (
        <LoadingIcon />
      ) : (
        <MarkdownContent content={props.content} />
      )}
    </div>
  );
}
