.login-page {
  display: flex;
  //justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  //flex-direction: column;

  .login-logo{
    transform: scale(1.2);
    margin-top: 70px;
  }
  .login-banner {
    width: 50%;
    height: 100%;
  }
  .login-banner-swiper {
    width: 100%;
    height: 100%;
  }
  .login-banner-img {
    width: 100%;
    height: 100%;
  }
  .login-context {
    width: 50%;
    height: 100%;
    display: flex;
    justify-content: center;
  }
  .login-context-con {
    width: calc(100% / 1.8);
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .login-context-title {
    margin: 0 auto;
    font-family: PingFang SC;
    font-size: 40px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
  }
  .login-context-sub-title {
    width: 130%;
    color: #b2abba;
    display: flex;
    align-items: center;
    padding-top: 25vh;
  }
  .login-context-sub-title-0 {
    height: 1px;
    width: 30%;
    background: #b2abba;
  }

  .login-context-sub-title-1 {
    height: 1px;
    width: 30%;
    background: #b2abba;
  }
  .login-context-sub-title-2 {
    margin-top: 5.5vh;
    text-align: center;
    font-family: PingFang SC;
    color: #ebeae9;
    line-height: 20px;
    letter-spacing: 2px;
    font-size: 30px;
    font-weight: bold;
  }

  .login-context-sub-title-3{
    font-size: 0.9rem;
    width: 50%;
    text-align: center;
  }

  .login-context-box {
    margin-top: 4vh;
    width: 100%;
    .login-context-box-icon {
      display: flex;
      justify-content: space-around;
      a:not(child) {
        margin-bottom: 20px;
      }
    }
  }
  .login-a {
    width: calc(100% / 3.3);
    height: 44px;
  }
  .login-google {
    //flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    border-radius: 10px;
    width: 100%;
    height: 44px;
    background: #fff;

    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    user-select: none;
    outline: none;
    border: none;
    color: var(--black);

    &[disabled] {
      cursor: not-allowed;
      opacity: 0.5;
    }

    &:hover,
    &:focus {
      border-color: var(--primary);
      filter: brightness(0.9);
    }
  }
  .login-context-bottom {
    margin: 20px auto 0;
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;

    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    letter-spacing: 0em;
    text-align: left;
    & > a {
      text-decoration: none;
    }
  }

  @media only screen and (max-width: 600px) {
    .login-banner {
      display: none;
    }
    .login-context {
      width: 100%;
    }
  }
}
