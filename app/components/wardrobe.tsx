import React, { useState, useRef, useEffect, useMemo } from "react";

import UploadImg from "../icons/uploadImg.svg";

import ReturnIcon from "../icons/return.svg";
import LoadingIcon from "../icons/three-dots.svg";
import LoadingButtonIcon from "../icons/loading.svg";

import MaxIcon from "../icons/max.svg";
import MinIcon from "../icons/min.svg";
import ImgWatch from "../icons/watch.svg";

import ImgDelete from "../icons/imgDelete.svg";

import ImgDown from "../icons/imgDown.svg";

import { useChatStore, useAppConfig } from "../store";

import { useMobileScreen, uploadImageToBlob } from "../utils";

import Locale from "../locales";

import { IconButton } from "./button";
import styles from "./wardrobe.module.scss";

import { useLocation, useNavigate } from "react-router-dom";
import { Path } from "../constant";

import { getClientConfig } from "../config/client";

import Image from "next/image";
import { ImageModal } from "./ui-lib";

//图片放大缩小查看
export function ImageMessageModal(props: { onClose: () => void; data: any }) {
  console.log(props.data);

  return (
    <div className="modal-mask-img">
      <ImageModal
        // url={props.data.downloadUrl}
        onClose={props.onClose}
        actions={[
          <IconButton
            // text={Locale.UI.Cancel}
            // icon={<CancelIcon />}
            key="cancel"
            onClick={() => {
              props.onClose();
            }}
          />,
          <IconButton
            type="primary"
            // text={Locale.UI.Confirm}
            // icon={<ConfirmIcon />}
            key="ok"
            // onClick={() => {
            //   chatStore.updateCurrentSession(
            //     (session) => (session.messages = messages),
            //   );
            //   props.onClose();
            // }}
          />,
        ]}
      >
        <div style={{ padding: 60, width: "100%", height: "80%" }}>
          <img
            src={props.data.imageUrl}
            className="postImg"
            alt={props.data.imageName}
            style={{
              objectFit: "contain",
              width: "100%",
              height: "100%",
            }}
            // priority={isPriority}
          />
        </div>
      </ImageModal>
    </div>
  );
}

function _Chat() {
  const chatStore = useChatStore();
  const session = chatStore.currentSession();
  const config = useAppConfig();

  const isMobileScreen = useMobileScreen();
  const navigate = useNavigate();

  const clientConfig = useMemo(() => getClientConfig(), []);

  const showMaxIcon = !isMobileScreen && !clientConfig?.isApp;

  const inputFileRef = useRef<any>(null);
  const [wardrobes, setWardrobes] = useState<any[]>([]);
  //uploading
  const [uploadLoading, setUploadLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  //mouseevent
  const [mouseId, setMouseId] = useState(0);

  //delete
  const [deleteId, setDeleteId] = useState<any[]>([]);

  //img show
  const [isImageShow, setIsImageShow] = useState("0");

  /**
   * fetch wardrobe
   */
  const fetchWardrobe = async () => {
    if (wardrobes.length == 0) {
      setIsLoading(true);
    }
    fetch("/api/wardrobe", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }).then(async (res) => {
      const data = await res.json();
      console.log("wardrobe", data);
      setWardrobes(data.data);
      setIsLoading(false);
      setUploadLoading(false);
    });
  };

  /**
   * delete wardrobe
   * @param id id
   */
  const deleteWardrobe = async (id: string) => {
    setDeleteId([...deleteId, id]);
    fetch(`/api/wardrobe/`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ id }),
    }).then(async (res) => {
      const data = await res.json();
      setDeleteId(
        deleteId.filter((item) => {
          return item !== id;
        }),
      );
      console.log("wardrobe", data);
      fetchWardrobe();
    });
  };

  useEffect(() => {
    fetchWardrobe();
  }, []);

  async function uploadImage() {
    await new Promise<string[]>((res, rej) => {
      // const fileInput: any = document.getElementById(
      //   "_wardrobe_upload_img_file_mod",
      // );

      if (!inputFileRef.current?.files) {
        throw new Error("No file selected");
      }
      const fileInput = inputFileRef.current;
      fileInput.multiple = true;
      fileInput.type = "file";
      fileInput.accept =
        "image/png, image/jpeg, image/webp, image/heic, image/heif";

      fileInput.onchange = async (event: any) => {
        console.log(event);
        const files = event.target.files;
        const imagesData: string[] = [];
        setUploadLoading(true);
        for (let i = 0; i < files.length; i++) {
          const file = event.target.files[i];
          const newBlob: any = await uploadImageToBlob(file);
          // 2. upload the image url to wardrobe
          const wardrobe = {
            imageName: file.name,
            imageUrl: newBlob.url,
            downloadUrl: newBlob.url,
          };
          const res = await fetch("/api/wardrobe", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(wardrobe),
          });
          const data = await res.json();
        }
        fetchWardrobe();
      };
      fileInput.click();
    });
  }

  //download
  function download(link: any) {
    const xhr = new XMLHttpRequest();
    xhr.open("GET", link);
    xhr.responseType = "blob";
    xhr.send();
    xhr.onload = function () {
      const fileBlob = xhr.response;
      console.log(fileBlob);
      const fileUrl = URL.createObjectURL(fileBlob);
      console.log(fileUrl);
      const ele = document.createElement("a");
      ele.setAttribute("href", fileUrl);
      ele.setAttribute("download", "");
      ele.click();
      ele.innerHTML = "下载";
      // hide the element
      ele.style.display = "none";
      document.body.appendChild(ele);
    };
  }

  return (
    <div className={styles.chat} key={session.id}>
      <div className="window-header" data-tauri-drag-region>
        {isMobileScreen && (
          <div className="window-actions">
            <div className={"window-action-button"}>
              <IconButton
                icon={<ReturnIcon />}
                bordered
                title={Locale.Chat.Actions.ChatList}
                onClick={() => navigate(Path.Home)}
              />
            </div>
          </div>
        )}

        <div className={`window-header-title ${styles["chat-body-title"]}`}>
          <div
            className={`window-header-main-title ${styles["chat-body-main-title"]}`}
            //onClickCapture={() => setIsEditingMessage(true)}
          >
            {Locale.Plugin.Name}
          </div>
          <div className="window-header-sub-title">
            {Locale.Wardrobe.SubTitle(wardrobes?.length || 0)}
          </div>
        </div>
        <div className="window-actions">
          <div className="window-action-button">
            <IconButton
              icon={uploadLoading ? <LoadingButtonIcon /> : <UploadImg />}
              bordered
              onClick={() => uploadImage()}
            />
            <input
              id="_wardrobe_upload_img_file_mod"
              type="file"
              ref={inputFileRef}
              accept="image/png, image/jpeg, image/webp, image/heic, image/heif"
              style={{
                display: "none",
              }}
            />
          </div>

          {showMaxIcon && (
            <div className="window-action-button">
              <IconButton
                icon={config.tightBorder ? <MinIcon /> : <MaxIcon />}
                bordered
                onClick={() => {
                  config.update(
                    (config) => (config.tightBorder = !config.tightBorder),
                  );
                }}
              />
            </div>
          )}
        </div>
      </div>
      <div className={styles["chat-body"]}>
        <div className={styles["wardrobe-content"]}>
          {isLoading ? (
            <LoadingIcon />
          ) : (
            <div className={styles["wardrobe-content-list"]}>
              {wardrobes?.map((wardrobe) => (
                <div
                  key={wardrobe.id}
                  className={styles["wardrobe-content-list-item"]}
                  onMouseEnter={() => setMouseId(wardrobe.id)}
                  onMouseLeave={() => setMouseId(0)}
                  style={{ backgroundColor: "white" }}
                >
                  <img
                    src={wardrobe.imageUrl}
                    alt={wardrobe.imageName}
                    width={isMobileScreen ? 300 : 273}
                    height={isMobileScreen ? 300 : 273}
                    style={{
                      opacity: deleteId.includes(wardrobe.id) ? 0.3 : 1,
                      objectFit: "contain",
                    }}
                  />
                  {deleteId.includes(wardrobe.id) ? (
                    <div
                      className={
                        styles["wardrobe-content-list-item-delete-loading"]
                      }
                    >
                      <LoadingIcon />
                    </div>
                  ) : (
                    <></>
                  )}
                  {mouseId == wardrobe.id ? (
                    <div
                      className={styles["wardrobe-content-list-item-action"]}
                    >
                      <IconButton
                        icon={<ImgWatch />}
                        className={
                          styles["wardrobe-content-list-item-action-item"]
                        }
                        bordered
                        onClick={() => {
                          setIsImageShow(wardrobe.id);
                        }}
                      />
                      <IconButton
                        icon={<ImgDown />}
                        className={
                          styles["wardrobe-content-list-item-action-item"]
                        }
                        bordered
                        onClick={() => {
                          download(wardrobe.downloadUrl);
                        }}
                      />
                      <IconButton
                        icon={<ImgDelete />}
                        className={
                          styles["wardrobe-content-list-item-action-item"]
                        }
                        bordered
                        onClick={() => deleteWardrobe(wardrobe.id)}
                      />
                    </div>
                  ) : (
                    <></>
                  )}
                  {wardrobe.id == isImageShow && (
                    <ImageMessageModal
                      onClose={() => {
                        setIsImageShow("0");
                      }}
                      data={wardrobe}
                    />
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export function WardrobePage() {
  const chatStore = useChatStore();
  const sessionIndex = chatStore.currentSessionIndex;
  const location = useLocation();

  return <_Chat key={sessionIndex}></_Chat>;
}
