/* eslint-disable @next/next/no-img-element */
"use client";
import styles from "./web-view-login.module.scss";
import "swiper/css";
import { useEffect } from "react";

import logo from "../icons/logo.png";
import banner1 from "../icons/SwiperBanner1.png";
import banner2 from "../icons/SwiperBanner2.png";
import GoogleIcon from "../icons/googleIcon.svg";
import AppleIcon from "../icons/appleIcon.svg";
import GitTab from "../icons/gitTab.svg";
import FaceBook from "../icons/faceBook.svg";
import GithubIcon from "../icons/githubIcon.svg";
import TwttierIcon from "../icons/twttierIcon.svg";

import { Autoplay, EffectFade, Pagination } from "swiper/modules";
import { IconButton } from "./button";
import { RegisterLink } from "@kinde-oss/kinde-auth-nextjs/components";
import { useKindeBrowserClient } from "@kinde-oss/kinde-auth-nextjs";
import { Loading } from "@/app/components/home";

export function WebViewLoginPage() {
  //客户端组件使用 kinde
  const { user, isLoading, accessTokenRaw, idTokenRaw } =
    useKindeBrowserClient();

  useEffect(() => {
    // 进入页面时设置 body 背景色为黑色
    document.body.style.background = "#060110";

    // 组件卸载时恢复为 #f0f2f5
    return () => {
      document.body.style.background = "#f0f2f5"; // 恢复默认颜色
    };
  }, []);
  if (isLoading) {
    console.log("isLoading", isLoading);
    return <Loading />;
    // return <div>Loading...</div>
  }
  if (user && user.id) {
    console.log("isLoading", isLoading);
    if (window.uni) {
      window.uni.getEnv((res: any) => {
        console.log("当前uni环境：" + JSON.stringify(res));
      });
      if (user && user.id) {
        const userInfo = {
          user: user,
          token: accessTokenRaw,
          idToken: idTokenRaw,
        };
        console.log("userInfo", JSON.stringify(userInfo));
        window.uni.reLaunch({
          url:
            "/pages/login/login?userInfo=" +
            encodeURIComponent(JSON.stringify(userInfo)),
        });
      }
    }
    return <Loading />;
  }

  return (
    <div className={styles["login-page"]}>
      <div className={styles["login-context"]}>
        <div className={styles["login-context-con"]}>
          {/* <img src={require("../icons/logo.png")} alt="logo" className={styles["login-logo"]} /> */}
          {/* <div className={styles["login-context-logo"]}> */}
          <img src={logo.src} alt="logo" className={styles["login-logo"]} />
          {/* </div> */}
          <span className={styles["login-context-sub-title-2"]}>Welcome !</span>
          <div className={styles["login-context-sub-title"]}>
            <div className={styles["login-context-sub-title-0"]}></div>
            <div className={styles["login-context-sub-title-3"]}>
              Or,sign up with:
            </div>
            <div className={styles["login-context-sub-title-1"]}></div>
          </div>
          <div className={styles["login-context-box"]}>
            <div className={styles["login-context-box-icon"]}>
              {/* <RegisterLink
                authUrlParams={{
                  connection_id: "conn_39f061905c734e61834c8295d300d8a0",
                }}
                postLoginRedirectURL="/#/web-view-login"
                className={styles["login-a"]}
              >
                <button className={styles["login-google"]}>
                  <GoogleIcon />
                </button>
              </RegisterLink> */}
              <RegisterLink
                authUrlParams={{
                  connection_id: "conn_700699aa6a274e25bcf5f8349830cd6e",
                }}
                postLoginRedirectURL="/#/web-view-login"
                className={styles["login-a"]}
              >
                <button className={styles["login-google"]}>
                  <AppleIcon />
                </button>
              </RegisterLink>
              <RegisterLink
                authUrlParams={{
                  connection_id: "conn_726d229e2cd24b0697f21e9668df662c",
                }}
                postLoginRedirectURL="/#/web-view-login"
                className={styles["login-a"]}
              >
                <button className={styles["login-google"]}>
                  <GithubIcon />
                </button>
              </RegisterLink>
            </div>
            <div className={styles["login-context-box-icon"]}>
              {/* <RegisterLink
                authUrlParams={{
                  connection_id: "conn_ff29124deb77474c926715b06dca12d4",
                }}
                postLoginRedirectURL="/#/web-view-login"
                className={styles["login-a"]}
              >
                <button className={styles["login-google"]}>
                  <GitTab />
                </button>
              </RegisterLink> */}
              <RegisterLink
                authUrlParams={{
                  connection_id: "conn_9315bb22798141b39472350f73849466",
                }}
                postLoginRedirectURL="/#/web-view-login"
                className={styles["login-a"]}
              >
                <button className={styles["login-google"]}>
                  <FaceBook />
                </button>
              </RegisterLink>
              <RegisterLink
                authUrlParams={{
                  connection_id: "conn_18cd7ca9e62f46e595c2d286a3191971",
                }}
                postLoginRedirectURL="/#/web-view-login"
                className={styles["login-a"]}
              >
                <button className={styles["login-google"]}>
                  <TwttierIcon />
                </button>
              </RegisterLink>
            </div>
          </div>
          {/* <div className={styles["login-context-bottom"]}>
            No account？<a href="https://kinde.com">Create one</a>
          </div> */}
        </div>
      </div>
    </div>
  );
}
