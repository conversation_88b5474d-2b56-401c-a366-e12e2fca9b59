"use client";

require("../polyfill");

import { useState, useEffect } from "react";

import styles from "./home.module.scss";

import BotIcon from "../icons/bot.svg";
import LoadingIcon from "../icons/three-dots.svg";

import { getCSSVar, useMobileScreen } from "../utils";

import dynamic from "next/dynamic";
import { ModelProvider, Path, SlotID } from "../constant";
import { ErrorBoundary } from "./error";

import { getISOLang, getLang } from "../locales";
import { useKindeBrowserClient } from "@kinde-oss/kinde-auth-nextjs";
import useWindowSize from "@rooks/use-window-size";
import { WebViewLoginPage } from "@/app/components/web-view-login";

import {
  HashRouter as Router,
  Routes,
  Route,
  useLocation,
  useNavigate,
  redirect,
} from "react-router-dom";
import { SideBar } from "./sidebar";
import { useAppConfig } from "../store/config";
import { AuthPage } from "./auth";
import { getClientConfig } from "../config/client";
import { ClientApi } from "../client/api";
import { useAccessStore } from "../store";
import { LoginPage } from "./login";
import { useSyncStore } from "../store/sync";

export function Loading(props: { noLogo?: boolean }) {
  return (
    <div className={styles["loading-content"] + " no-dark"}>
      {!props.noLogo && <BotIcon />}
      <LoadingIcon />
    </div>
  );
}

const Settings = dynamic(async () => (await import("./settings")).Settings, {
  loading: () => <Loading noLogo />,
});

const Chat = dynamic(async () => (await import("./chat")).Chat, {
  loading: () => <Loading noLogo />,
});

const NewChat = dynamic(async () => (await import("./new-chat")).NewChat, {
  loading: () => <Loading noLogo />,
});

const Mask = dynamic(async () => (await import("./mask")).MaskPage, {
  loading: () => <Loading noLogo />,
});

const WardrobePage = dynamic(
  async () => (await import("./wardrobe")).WardrobePage,
  {
    loading: () => <Loading noLogo />,
  },
);

const Guide = dynamic(async () => (await import("./guide")).Guide, {
  loading: () => <Loading noLogo />,
});

export function useSwitchTheme() {
  const config = useAppConfig();

  useEffect(() => {
    document.body.classList.remove("light");
    document.body.classList.remove("dark");

    if (config.theme === "dark") {
      document.body.classList.add("dark");
    } else if (config.theme === "light") {
      document.body.classList.add("light");
    }

    const metaDescriptionDark = document.querySelector(
      'meta[name="theme-color"][media*="dark"]',
    );
    const metaDescriptionLight = document.querySelector(
      'meta[name="theme-color"][media*="light"]',
    );

    if (config.theme === "auto") {
      metaDescriptionDark?.setAttribute("content", "#151515");
      metaDescriptionLight?.setAttribute("content", "#fafafa");
    } else {
      const themeColor = getCSSVar("--theme-color");
      metaDescriptionDark?.setAttribute("content", themeColor);
      metaDescriptionLight?.setAttribute("content", themeColor);
    }
  }, [config.theme]);
}

function useHtmlLang() {
  useEffect(() => {
    const lang = getISOLang();
    const htmlLang = document.documentElement.lang;

    if (lang !== htmlLang) {
      document.documentElement.lang = lang;
    }
  }, []);
}

const useHasHydrated = () => {
  const [hasHydrated, setHasHydrated] = useState<boolean>(false);

  useEffect(() => {
    setHasHydrated(true);
  }, []);

  return hasHydrated;
};

const loadAsyncGoogleFont = () => {
  const linkEl = document.createElement("link");
  const proxyFontUrl = "/google-fonts";
  const remoteFontUrl = "https://fonts.googleapis.com";
  const googleFontUrl =
    getClientConfig()?.buildMode === "export" ? remoteFontUrl : proxyFontUrl;
  linkEl.rel = "stylesheet";
  linkEl.href =
    googleFontUrl +
    "/css2?family=" +
    encodeURIComponent("Noto Sans:wght@300;400;700;900") +
    "&display=swap";
  document.head.appendChild(linkEl);
};

function Screen() {
  const config = useAppConfig();
  const location = useLocation();
  const isHome = location.pathname === Path.Home;
  const isLogin = location.pathname === Path.Login;

  const isMobileScreen = useMobileScreen();
  const shouldTightBorder =
    getClientConfig()?.isApp || (config.tightBorder && !isMobileScreen);

  const isGuide = location.pathname === Path.Guide;
  const guideShow = localStorage.getItem("last-input");
  const navigate = useNavigate();
  const { innerWidth } = useWindowSize();

  useEffect(() => {
    if (!guideShow && (innerWidth || 600) > 600) {
      navigate(Path.Guide, { state: { fromHome: true } });
    }
  }, []);

  useEffect(() => {
    loadAsyncGoogleFont();
  }, []);

  return (
    <div
      className={
        styles.container +
        ` ${shouldTightBorder ? styles["tight-container"] : styles.container} ${
          getLang() === "ar" ? styles["rtl-screen"] : ""
        }`
      }
    >
      <SideBar className={isHome ? styles["sidebar-show"] : ""} />

      <div className={styles["window-content"]} id={SlotID.AppBody}>
        <Routes>
          <Route path={Path.Home} element={<Chat />} />
          <Route path={Path.Masks} element={<Mask />} />
          <Route path={Path.NewChat} element={<NewChat />} />
          <Route path={Path.Wardrobe} element={<WardrobePage />} />
          <Route path={Path.Chat} element={<Chat />} />
          <Route path={Path.Settings} element={<Settings />} />
          <Route path={Path.Guide} element={<Guide />} />
        </Routes>
      </div>
    </div>
  );
}

export function useLoadData() {
  const config = useAppConfig();

  var api: ClientApi;
  if (config.modelConfig.model.startsWith("gemini")) {
    api = new ClientApi(ModelProvider.GeminiPro);
  } else {
    api = new ClientApi(ModelProvider.GPT);
  }
  useEffect(() => {
    (async () => {
      const models = await api.llm.models();
      config.mergeModels(models);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
}

export function Home() {
  useSwitchTheme();
  useLoadData();
  useHtmlLang();
  const syncStore = useSyncStore();

  //客户端组件使用 kinde
  const { user, isLoading } = useKindeBrowserClient();

  useEffect(() => {
    console.log("[Config] got config from build time", getClientConfig());
    useAccessStore.getState().fetch();
  }, []);

  useEffect(() => {
    const couldSync = syncStore.cloudSync();
    if (!user || !couldSync) {
      return;
    }
    if (user && user.id) {
      localStorage.setItem("kinde_uid", user.id);
    }
    // 如果没有同步过，立即同步一次
    if (sessionStorage.getItem("synced-store") !== user.id) {
      syncStore.sync().then(() => {
        console.log("[Sync] chat synced");
        sessionStorage.setItem("synced-store", user.id);
      });
    }
    // 定时每 5 分钟同步一次
    const interval = setInterval(
      () => {
        syncStore.sync().then(() => {
          console.log("[Sync] chat synced");
        });
      },
      5 * 60 * 1000,
    );
    return () => clearInterval(interval);
  }, [syncStore, user]);

  if (!useHasHydrated() || isLoading) {
    return <Loading />;
  }

  let isWebViewLogin = false;
  if (typeof window !== "undefined") {
    isWebViewLogin = window.location.href.includes(Path.WebViewLoginPage);
    console.log("isWebViewLogin", isWebViewLogin);
  }

  return (
    <ErrorBoundary>
      <Router>
        {isWebViewLogin ? (
          <WebViewLoginPage />
        ) : user ? (
          <Screen />
        ) : (
          <LoginPage />
        )}
      </Router>
    </ErrorBoundary>
  );
}
