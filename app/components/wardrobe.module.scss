@import "../styles/animation.scss";

.chat {
  display: flex;
  flex-direction: column;
  position: relative;
  height: 100%;
}
.chat-body {
  flex: 1;
  overflow: auto;
  overflow-x: hidden;
  padding: 20px;
  padding-bottom: 40px;
  position: relative;
  overscroll-behavior: none;
}

.wardrobe-content {
  //height: 100%;
  //width: 80vw;
  padding: 40px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  //overflow-y: auto;
  overflow: auto;
  overflow-x: hidden;
}

.wardrobe-content-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 16px;
}

.wardrobe-content-list-item {
  position: relative;
}
// .wardrobe-content-list-item:hover {
//   opacity: 0.9;
// }
.wardrobe-content-list-item-action {
  width: 40%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: absolute;
  right: 10px;
  top: 10px;
}
.wardrobe-content-list-item-action-item {
  width: 32px;
  height: 32px;
}

.wardrobe-content-list-item-delete-loading {
  position: absolute;
  left: 50%;
  top: 45%;
  transform: translateX(-50%);
}
//watch
.wardrobe-content-list-item-watch {
  position: fixed;
  // top: 50%;
  // left: 50%;
}

@media screen and (max-width: 600px) {
  .wardrobe-content-list {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    grid-gap: 16px;
  }
  // .wardrobe-content-list-item-action-item {
  //   width: 16px;
  //   height: 16px;
  // }
}
