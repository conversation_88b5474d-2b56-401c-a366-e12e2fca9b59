/* eslint-disable @next/next/no-img-element */
"use client";
import styles from "./login.module.scss";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";

import banner1 from "../icons/SwiperBanner1.png";
import banner2 from "../icons/SwiperBanner2.png";
import GoogleIcon from "../icons/googleIcon.svg";
import AppleIcon from "../icons/appleIcon.svg";
import GitTab from "../icons/gitTab.svg";
import FaceBook from "../icons/faceBook.svg";
import GithubIcon from "../icons/githubIcon.svg";
import TwttierIcon from "../icons/twttierIcon.svg";

import { Autoplay, EffectFade, Pagination } from "swiper/modules";
import { IconButton } from "./button";
import { RegisterLink } from "@kinde-oss/kinde-auth-nextjs/components";

export function LoginPage() {
  return (
    <div className={styles["login-page"]}>
      <div className={styles["login-banner"]}>
        <Swiper
          className={styles["login-banner-swiper"]}
          //effect={"fade"}
          // install Swiper modules
          modules={[Autoplay, Pagination, EffectFade]}
          spaceBetween={0}
          slidesPerView={1}
          autoplay={{
            delay: 5000,
            disableOnInteraction: false,
          }}
          pagination={{ clickable: true }}
          //onSlideChange={() => console.log("slide change")}
          //onSwiper={(swiper) => console.log(swiper)}
          loop={true}
        >
          <SwiperSlide>
            <img
              src={banner2.src}
              alt="banner2"
              className={styles["login-banner-img"]}
            />
          </SwiperSlide>
          <SwiperSlide>
            <img
              src={banner1.src}
              alt="banner1"
              className={styles["login-banner-img"]}
            />
          </SwiperSlide>
        </Swiper>
      </div>
      <div className={styles["login-context"]}>
        <div className={styles["login-context-con"]}>
          <span className={styles["login-context-title"]}>Stylenow.ai</span>
          <span className={styles["login-context-sub-title"]}>Hey friend!</span>
          <span className={styles["login-context-sub-title-2"]}>
            Welcome back
          </span>
          <div className={styles["login-context-box"]}>
            <div className={styles["login-context-box-icon"]}>
              <RegisterLink
                authUrlParams={{
                  connection_id: "conn_39f061905c734e61834c8295d300d8a0",
                }}
                className={styles["login-a"]}
              >
                <button className={styles["login-google"]}>
                  <GoogleIcon />
                </button>
              </RegisterLink>
              <RegisterLink
                authUrlParams={{
                  connection_id: "conn_700699aa6a274e25bcf5f8349830cd6e",
                }}
                className={styles["login-a"]}
              >
                <button className={styles["login-google"]}>
                  <AppleIcon />
                </button>
              </RegisterLink>
              <RegisterLink
                authUrlParams={{
                  connection_id: "conn_ff29124deb77474c926715b06dca12d4",
                }}
                className={styles["login-a"]}
              >
                <button className={styles["login-google"]}>
                  <GitTab />
                </button>
              </RegisterLink>
            </div>
            <div className={styles["login-context-box-icon"]}>
              <RegisterLink
                authUrlParams={{
                  connection_id: "conn_9315bb22798141b39472350f73849466",
                }}
                className={styles["login-a"]}
              >
                <button className={styles["login-google"]}>
                  <FaceBook />
                </button>
              </RegisterLink>
              <RegisterLink
                authUrlParams={{
                  connection_id: "conn_726d229e2cd24b0697f21e9668df662c",
                }}
                className={styles["login-a"]}
              >
                <button className={styles["login-google"]}>
                  <GithubIcon />
                </button>
              </RegisterLink>
              <RegisterLink
                authUrlParams={{
                  connection_id: "conn_18cd7ca9e62f46e595c2d286a3191971",
                }}
                className={styles["login-a"]}
              >
                <button className={styles["login-google"]}>
                  <TwttierIcon />
                </button>
              </RegisterLink>
            </div>
          </div>
          {/* <div className={styles["login-context-bottom"]}>
            No account？<a href="https://kinde.com">Create one</a>
          </div> */}
        </div>
      </div>
    </div>
  );
}
