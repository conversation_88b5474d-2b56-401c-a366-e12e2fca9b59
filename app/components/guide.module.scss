.guide-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  flex-direction: column;

  .guide-content {
    position: relative;
    background: var(--guide-bg);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    box-shadow: 0px 5px 32px 0px #19213d17;
    //padding: 48px, 32px, 48px, 32px;
    border-radius: 16px;
    gap: 10px;
    max-width: calc(100% - 100px);
    width: 780px;
    min-height: 460px;
  }
  .guide-backgroung {
    position: absolute;
    right: 0px;
    bottom: 0px;
    border-radius: 16px;
    overflow: hidden;
    & > svg {
      margin-bottom: -6px;
      width: 100%;
      height: 100%;
    }
  }
  .guide-content-form {
    z-index: 1;
    max-width: 460px;
    height: calc(100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 3vh;
    gap: 24px;
  }

  .guide-content-title {
    font-family: PingFang SC;
    font-size: 40px;
    font-weight: 600;
    line-height: 52px;
    letter-spacing: 0em;
    text-align: center;
  }
  .guide-content-sub-title {
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    letter-spacing: 0em;
    text-align: center;
    color: #a8a8a8;
  }

  .chat-input-panel {
    position: relative;
    width: calc(90%);
    color: var(--black);
    padding: 0 20px;
    //margin: 0 0vh;
    //padding-top: 10px;
    border-radius: 16px;
    overflow: hidden;
    box-sizing: border-box;
    background-color: var(--white);
    flex-direction: column;
    border: 1px solid #f0f2f5;
    box-shadow: 0px 2px 4px 0px #19213d14;
  }
  .chat-input {
    height: 100%;
    width: 100%;
    //border-radius: 10px;
    border: none;
    background-color: var(--white);
    color: var(--black);
    font-family: inherit;
    //padding: 10px 90px 10px 14px;
    resize: none;
    outline: none;
    box-sizing: border-box;
    min-height: 58px;
    text-align: left;
  }

  .chat-input-send {
    background-color: var(--primary);
    color: white;
    position: absolute;
    right: 10px;
    bottom: 10px;
  }

  @media only screen and (max-width: 600px) {
    .chat-input {
      font-size: 16px;
    }

    .chat-input-send {
      bottom: 4px;
      right: 6px;
    }
    .guide-content {
      min-height: 260px;
    }
  }
}
