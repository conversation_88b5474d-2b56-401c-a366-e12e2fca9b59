"use client";
import { marked } from "marked";
import { useEffect, useRef, useState } from "react";
import "../../styles/share.scss";

export default function Share({
  params,
}: Readonly<{ params: { id: string } }>) {
  const [messages, setMessages] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    fetch(`/api/share?share_id=${params.id}`)
      .then(async (data) => {
        const messages = await data.json();
        setMessages(messages);
        setLoading(false);
      })
      .catch((e) => {
        console.error(JSON.stringify(e));
        setLoading(false);
      });
  }, [params.id]);

  if (!messages) {
    return (
      <div>
        <p>{loading ? "loading" : "Dialogue not found"}</p>
      </div>
    );
  }

  return (
    <div style={{ boxSizing: "border-box", width: "100vw" }}>
      {messages?.map((message: any) => {
        return (
          <TextSection
            key={message.id}
            role={message.role}
            content={message.content}
          />
        );
      })}
    </div>
  );
}

const TextSection = ({ role, content }: { role: string; content: string }) => {
  const [imageWidth, setImageWidth] = useState(0);
  const imageRef = useRef<HTMLDivElement>(null);

  const matchImageRegex = /!\[.*?\]\((.*?)\)/g;
  const contentSection = (content: string | any[]) => {
    if (Array.isArray(content)) {
      return content?.map((item, index) => {
        return item.type === "text" ? (
          <div
            key={item.id + index.toString()}
            style={{ float: role === "user" ? "right" : "left" }}
            dangerouslySetInnerHTML={{ __html: marked(item.text) }}
          />
        ) : (
          <img
            key={item.id + index.toString()}
            src={item.image_url.url}
            alt="user image"
            width={imageWidth}
            height="auto"
          />
        );
      });
    } else {
      let imageUrls = [];
      let matched;

      while ((matched = matchImageRegex.exec(content)) !== null) {
        imageUrls.push(matched[1]);
      }

      let contentWithNoImages = content.replace(matchImageRegex, "");

      return (
        <>
          <div
            style={{ float: role === "user" ? "right" : "left" }}
            dangerouslySetInnerHTML={{ __html: marked(contentWithNoImages) }}
          />
          {imageUrls &&
            imageUrls?.map((url: string, index) => {
              return (
                <img
                  key={url + index.toString()}
                  src={url}
                  alt="assistant image"
                  width={imageWidth}
                  height="auto"
                />
              );
            })}
        </>
      );
    }
  };

  useEffect(() => {
    if (imageRef.current) {
      setImageWidth(imageRef.current?.offsetWidth);
    }
  }, []);

  return (
    <div
      style={{
        backgroundColor: role === "user" ? "#343541" : "#434654",
        color: "#f3f4f6",
        padding: "1rem",
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: role === "user" ? "row-reverse" : "row",
          gap: "1rem",
          margin: "0 auto",
          maxWidth: "800px",
        }}
      >
        <p style={{ flex: 1, textAlign: role === "user" ? "right" : "left" }}>
          {role === "user" ? "user" : "assistant"}
        </p>
        <div ref={imageRef} style={{ flex: 5 }}>
          {contentSection(content)}
        </div>
      </div>
    </div>
  );
};
