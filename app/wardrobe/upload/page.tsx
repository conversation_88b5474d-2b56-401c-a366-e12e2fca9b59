"use client";

import { uploadImageToBlob } from "@/app/utils";
import { useState, useRef, useEffect } from "react";

export default function AvatarUploadPage() {
  const inputFileRef = useRef<HTMLInputElement>(null);
  const [wardrobes, setWardrobes] = useState<any[]>([]);

  /**
   * fetch wardrobe
   */
  const fetchWardrobe = async () => {
    fetch("/api/wardrobe", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    }).then(async (res) => {
      const data = await res.json();
      console.log("wardrobe", data);
      setWardrobes(data.data);
    });
  };

  /**
   * delete wardrobe
   * @param id id
   */
  const deleteWardrobe = async (id: string) => {
    fetch(`/api/wardrobe/`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ id }),
    }).then(async (res) => {
      const data = await res.json();
      console.log("wardrobe", data);
      fetchWardrobe();
    });
  };

  useEffect(() => {
    fetchWardrobe();
  }, []);

  return (
    <div style={{ display: "flex", flexDirection: "column" }}>
      <div>
        <h1>Upload Your Wardrobe</h1>

        <form
          onSubmit={async (event) => {
            event.preventDefault();

            if (!inputFileRef.current?.files) {
              throw new Error("No file selected");
            }

            const file = inputFileRef.current.files[0];

            // 1. upload the image to the blob store
            const newBlob = await uploadImageToBlob(file);

            // 2. upload the image url to wardrobe
            const wardrobe = {
              imageName: file.name,
              imageUrl: newBlob.url,
              downloadUrl: newBlob.url,
            };
            const res = await fetch("/api/wardrobe", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(wardrobe),
            });
            const data = await res.json();
            console.log("wardrobe", data);
            fetchWardrobe();
          }}
        >
          <input name="file" ref={inputFileRef} type="file" required />
          <button type="submit">Upload</button>
        </form>
      </div>
      <div>
        <h1>Your Wardrobe</h1>
        <div>
          {wardrobes.map((wardrobe) => (
            <div key={wardrobe.id}>
              <img
                src={wardrobe.imageUrl}
                alt={wardrobe.imageName}
                style={{ maxHeight: 100, maxWidth: 100 }}
              />
              &nbsp;
              <a href={wardrobe.downloadUrl} download={wardrobe.imageName}>
                {wardrobe.imageName}
              </a>
              &nbsp;
              <button onClick={() => deleteWardrobe(wardrobe.id)}>
                Delete
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
