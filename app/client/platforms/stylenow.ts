"use client";
import {
  Api<PERSON><PERSON>,
  DEFAULT_API_HOST,
  DEFAULT_MODELS,
  OpenaiPath,
  REQUEST_TIMEOUT_MS,
  ServiceProvider,
  CREATE_IMAGE_DALLE_NAME,
  CREATE_IMAGE_DALLE_FUNCTION,
  FUNCTION_NAMES,
  GET_IMAGE_FOR_SYTLING_ADVICE_FUNCTION,
  GET_IMAGE_FOR_SYTLING_ADVICE_NAME,
} from "@/app/constant";
import { useAccessStore, useAppConfig, useChatStore } from "@/app/store";

import {
  ChatOptions,
  getHeaders,
  LLMApi,
  LLMModel,
  LLMUsage,
  MultimodalContent,
  OpenAIToolCall,
  RequestMessage,
} from "../api";
import Locale from "../../locales";
import {
  EventStreamContentType,
  fetchEventSource,
} from "@fortaine/fetch-event-source";
import { getClientConfig } from "@/app/config/client";
import { makeAzurePath } from "@/app/azure";
import { getMessageTextContent, getMessageImages } from "@/app/utils";
import { serverError } from "@/app/utils/format";

export interface OpenAIListModelResponse {
  object: string;
  data: Array<{
    id: string;
    object: string;
    root: string;
  }>;
}

/**
 * OpenAI create image response
 */
export interface OpenAICreateImageResponse {
  created: string;
  data: Array<{
    b64_json?: string; // The base64-encoded JSON of the generated image, if response_format is b64_json.
    url?: string; // The URL of the generated image, if response_format is url (default).
    revised_prompt?: string; // The prompt that was used to generate the image, if there was any revision to the prompt.
  }> | null;
}

const defaultTextModel = "gpt-4o";
// const defaultTextModel = "gpt-4-1106-preview";
const defaultVisionModel = "gpt-4-turbo-2024-04-09";

export class StylenowApi implements LLMApi {
  private disableListModels = true;

  path(path: string): string {
    const accessStore = useAccessStore.getState();
    // is mock
    if (process.env.MOCK === "true") {
      console.log("[Stylenow] use mock api");
      return ["/api/mock", path].join("/");
    }

    const isAzure = accessStore.provider === ServiceProvider.Azure;

    if (isAzure && !accessStore.isValidAzure()) {
      throw Error(
        "incomplete azure config, please check it in your settings page",
      );
    }

    let baseUrl = isAzure ? accessStore.azureUrl : accessStore.openaiUrl;

    if (baseUrl.length === 0) {
      const isApp = !!getClientConfig()?.isApp;
      baseUrl = isApp
        ? DEFAULT_API_HOST + "/proxy" + ApiPath.OpenAI
        : ApiPath.OpenAI;
    }

    if (baseUrl.endsWith("/")) {
      baseUrl = baseUrl.slice(0, baseUrl.length - 1);
    }
    if (!baseUrl.startsWith("http") && !baseUrl.startsWith(ApiPath.OpenAI)) {
      baseUrl = "https://" + baseUrl;
    }

    if (isAzure) {
      path = makeAzurePath(path, accessStore.azureApiVersion);
    }

    console.log("[Proxy Endpoint] ", baseUrl, path);

    return [baseUrl, path].join("/");
  }

  /**
   * extract message from openai response
   * @param res
   * @returns
   */
  extractMessage(res: any) {
    return res.choices?.at(0)?.message?.content ?? "";
  }

  /**
   * check if the last message contains image
   * @param messages
   * @returns
   */
  isLastMessageImage(messages: RequestMessage[]) {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage.content) {
      return getMessageImages(lastMessage).length > 0;
    }
    return false;
  }

  /**
   * chat with OpenAI
   * @param options
   * @param prevContent previous content
   */
  async chat(options: ChatOptions, prevContent = "") {
    // const visionModel = isVisionModel(options.config.model);
    const visionModel = false;
    const messages = options.messages.map((v) => {
      const msg: RequestMessage = {
        role: v.role,
        content: visionModel ? v.content : getMessageTextContent(v, true),
      };
      if (v.tool_calls) {
        msg.tool_calls = v.tool_calls;
      }
      if (v.tool_call_id) {
        msg.tool_call_id = v.tool_call_id;
      }
      return msg;
    });

    const modelConfig = {
      ...useAppConfig.getState().modelConfig,
      ...useChatStore.getState().currentSession().mask.modelConfig,
      ...{
        model: visionModel ? defaultVisionModel : defaultTextModel,
      },
    };

    const requestPayload: any = {
      messages,
      stream: options.config.stream,
      model: modelConfig.model,
      temperature: modelConfig.temperature,
      presence_penalty: modelConfig.presence_penalty,
      frequency_penalty: modelConfig.frequency_penalty,
    };
    if (visionModel) {
      requestPayload.max_tokens = 1024;
    } else {
      requestPayload.tools = [
        { type: "function", function: CREATE_IMAGE_DALLE_FUNCTION },
        { type: "function", function: GET_IMAGE_FOR_SYTLING_ADVICE_FUNCTION },
      ];
    }

    console.log("[Request] openai payload: ", requestPayload);

    const shouldStream = !!options.config.stream;
    const controller = new AbortController();
    options.onController?.(controller);

    try {
      const chatPath = this.path(OpenaiPath.ChatPath);
      const chatPayload = {
        method: "POST",
        body: JSON.stringify(requestPayload),
        signal: controller.signal,
        headers: getHeaders(),
      };

      // make a fetch request
      const requestTimeoutId = setTimeout(
        () => controller.abort(),
        REQUEST_TIMEOUT_MS,
      );

      let responseText = prevContent;
      let remainText = "";
      let imgLink = "";
      const toolCalls: Array<OpenAIToolCall> = [];
      let hasToolCalling = false; // check if there is a tool calling
      let finished = false;

      // animate response to make it looks smooth
      function animateResponseText() {
        if (!hasToolCalling && (finished || controller.signal.aborted)) {
          responseText += remainText;
          if (controller.signal.aborted) {
            console.log("[Response Animation] aborted");
            options.onError?.(new Error("Request aborted"));
          } else {
            console.log("[Response Animation] finished");
          }
          return;
        }

        if (remainText.length > 0) {
          const fetchCount = Math.max(1, Math.round(remainText.length / 60));
          const fetchText = remainText.slice(0, fetchCount);
          responseText += fetchText;
          remainText = remainText.slice(fetchCount);
          options.onUpdate?.(responseText, fetchText);
        }

        requestAnimationFrame(animateResponseText);
      }

      // start animaion
      animateResponseText();

      const finish = () => {
        if (!finished) {
          finished = true;
          options.onFinish(responseText + remainText);
        }
      };

      controller.signal.onabort = finish;
      const that = this;

      // make a chat request
      if (shouldStream) {
        fetchEventSource(chatPath, {
          ...chatPayload,
          async onopen(res) {
            clearTimeout(requestTimeoutId);
            const contentType = res.headers.get("content-type");
            console.log(
              "[OpenAI] request response content type: ",
              contentType,
            );

            if (contentType?.startsWith("text/plain")) {
              responseText = await res.clone().text();
              return finish();
            }

            // error handling
            if (
              !res.ok ||
              !res.headers
                .get("content-type")
                ?.startsWith(EventStreamContentType) ||
              res.status !== 200
            ) {
              const responseTexts = [responseText];
              let extraInfo = await res.clone().text();
              try {
                const resJson = await res.clone().json();
                extraInfo =
                  resJson?.message ||
                  resJson?.error?.message ||
                  resJson?.cause?.message;
              } catch {}

              if (res.status === 401) {
                responseTexts.push(Locale.Error.Unauthorized);
              }

              responseTexts.push(serverError(extraInfo));

              responseText = responseTexts.join("\n\n");

              return finish();
            }
          },
          onmessage(msg) {
            if (msg.data === "[DONE]" || finished) {
              return finish();
            }
            const text = msg.data;
            try {
              const json = JSON.parse(text) as {
                choices: Array<{
                  delta: {
                    content: string;
                    tool_calls?: Array<OpenAIToolCall>;
                  };
                  finish_reason: string | null;
                }>;
              };
              const content = json.choices[0]?.delta?.content;
              if (content) {
                // check if the content contains image markdown, add the image link to remainText util it's full.
                const imgStartIdx = content.indexOf("![");
                const imgEndIdx = content.indexOf(")");
                if (imgLink) {
                  // image link end
                  if (imgEndIdx > -1) {
                    // append chunks to image link.
                    imgLink += content.substring(0, imgEndIdx);
                    remainText += imgLink;
                    imgLink = "";
                  } else {
                    imgLink += content;
                  }
                }
                // image
                if (imgStartIdx > -1) {
                  // the content contains the full image.
                  if (imgEndIdx > imgStartIdx) {
                    remainText += content;
                  } else {
                    // partial image link
                    imgLink += content.substring(imgStartIdx);
                    if (imgStartIdx > 0) {
                      // add normal text content
                      remainText += content.substring(0, imgStartIdx);
                    }
                  }
                } else if (!imgLink) {
                  // normal content
                  remainText += content;
                }
              } else if (json.choices[0]?.delta?.tool_calls) {
                // append the chunks of tool calls
                that.appendToolCalls(json, toolCalls);
              }
              // finish reason
              const finishReason = json.choices[0]?.finish_reason;
              if (finishReason) {
                console.log("[Finish Reason] ", finishReason);
              }
              // tools calls
              if (finishReason === "tool_calls" && toolCalls.length) {
                hasToolCalling = true;
                console.log("[tool_calls]: ", toolCalls);
                that.runToolCalls(toolCalls, responseText, that, options);
              }
              // stop
              if (finishReason === "stop") {
                return finish();
              }
            } catch (e) {
              console.error("[Request] parse error", text, e);
              options.onError?.(e as Error);
            }
          },
          onclose() {
            finish();
          },
          onerror(e) {
            options.onError?.(e);
            throw e;
          },
          openWhenHidden: true,
        });
      } else {
        const res = await fetch(chatPath, chatPayload);
        clearTimeout(requestTimeoutId);

        const resJson = await res.json();
        const message = this.extractMessage(resJson);
        if (
          resJson.choices[0]?.finish_reason === "tool_calls" &&
          resJson.choices[0]?.message?.tool_calls
        ) {
          this.runToolCalls(
            resJson.choices[0].message.tool_calls,
            message,
            that,
            options,
          );
        } else {
          options.onFinish(message);
        }
      }
    } catch (e) {
      console.log("[Request] failed to make a chat request", e);
      options.onError?.(e as Error);
    }
  }

  /**
   * run tool calls
   * @param toolCalls
   * @param that
   * @param options
   */
  private runToolCalls(
    toolCalls: OpenAIToolCall[],
    content: string,
    that: this,
    options: ChatOptions,
  ) {
    for (const tool of toolCalls) {
      console.log("[Tool Call] ", tool);
      try {
        const name = tool.function.name;
        if (name === CREATE_IMAGE_DALLE_NAME) {
          // if the arguments contain multiple items, merge them into one
          // example: {"items": [{"prompt": "a casual outfit with a khaki cardigan, white t-shirt, denim jeans and white sneakers", "size": "1024x1024", "quality": "standard"}]}{"items": [{"prompt": "a professional office outfit with a khaki cardigan, white or light-colored shirt, dark straight or pencil trousers, and high heels", "size": "1024x1024", "quality": "standard"}]}
          // to: {"items": [{"prompt": "a casual outfit with a khaki cardigan, white t-shirt, denim jeans and white sneakers", "size": "1024x1024", "quality": "standard"}, {"prompt": "a professional office outfit with a khaki cardigan, white or light-colored shirt, dark straight or pencil trousers, and high heels", "size": "1024x1024", "quality": "standard"}]}
          if (tool.function.arguments.includes(`]}{"items": [`)) {
            tool.function.arguments = tool.function.arguments.replaceAll(
              `]}{"items": [`,
              ", ",
            );
          }
          // parse the arguments
          console.log("[Tool Call] args", tool.function.arguments);
          const args = JSON.parse(tool.function.arguments);
          that.createImageInDalle(options, args).then((images) => {
            // append tool_calls response to assistant message
            options.messages.push({
              role: "assistant",
              content,
              tool_calls: toolCalls,
            });
            // append the images to the chat
            options.messages.push({
              role: "tool",
              content: JSON.stringify(images),
              tool_call_id: tool.id,
            });
            // continue the chat
            if (content.length > 0) {
              content += "\n\n";
            }
            that.chat(options, content);
          });
        } else if (name === GET_IMAGE_FOR_SYTLING_ADVICE_NAME) {
          // if the arguments contain multiple items, merge them into one
          if (tool.function.arguments.includes(`]}{"images": [`)) {
            tool.function.arguments = tool.function.arguments.replaceAll(
              `]}{"images": [`,
              ", ",
            );
          }
          // parse the arguments
          console.log("[Tool Call] args", tool.function.arguments);
          const args = JSON.parse(tool.function.arguments);
          // tool call
          that
            .getImageStylingAdvice(options, args)
            .then((res) => {
              // append tool_calls response to assistant message
              options.messages.push({
                role: "assistant",
                content,
                tool_calls: toolCalls,
              });
              // append the res to the chat
              options.messages.push({
                role: "tool",
                content: res,
                tool_call_id: tool.id,
              });
              // continue the chat
              if (content.length > 0) {
                content += "\n\n";
              }
              that.chat(options, content);
            })
            .catch((e) => {
              console.log("[Tool Call] failed to make a tool call", e);
              options.onError?.(e as Error);
            });
        }
      } catch (e) {
        console.log("[Tool Call] failed to make a tool call", e);
        options.onError?.(e as Error);
      }
    }
  }

  /**
   * append tool calls from json chunks
   * @param json json chunks
   * @param toolCalls
   * @returns
   */
  appendToolCalls(
    json: {
      choices: {
        delta: {
          content: string;
          tool_calls?: Array<OpenAIToolCall>;
        };
        finish_reason: string | null;
      }[];
    },
    toolCalls: OpenAIToolCall[],
  ) {
    if (!json.choices[0]?.delta?.tool_calls) {
      return;
    }
    for (let i = 0; i < json.choices[0].delta.tool_calls.length; i++) {
      const toolChunks = json.choices[0].delta.tool_calls[i];
      if (toolCalls.length < i + 1) {
        toolCalls.push({
          id: "",
          type: "function",
          function: { name: "", arguments: "" },
        });
      }
      const toolCall = toolCalls[i];
      if (toolChunks.id) {
        toolCall.id += toolChunks.id;
      }
      if (toolChunks.type && toolCall.type !== "function") {
        toolCall.type += toolChunks.type;
      }
      if (
        toolChunks.function?.name &&
        !FUNCTION_NAMES.includes(toolCall.function.name)
      ) {
        toolCall.function.name += toolChunks.function.name;
      }
      if (toolChunks.function?.arguments) {
        toolCall.function.arguments += toolChunks.function.arguments;
      }
    }
  }

  /**
   * create image in dalle
   * @param options
   * @param args
   * @returns
   */
  async createImageInDalle(
    options: ChatOptions,
    args: {
      items: Array<{ prompt: string; size: string; quality: string }>;
    },
  ) {
    if (!args?.items) {
      console.log("[Create Image Dalle] no items");
      return;
    }
    const imagePath = this.path(OpenaiPath.CreateImage);
    const images = await Promise.all(
      Array.from(args.items).map(async (img) => {
        let prompt = img.prompt;
        const size = img.size;
        const quality = img.quality;

        try {
          const requestPayload = {
            model: "dall-e-3",
            prompt,
            size,
            n: 1,
            quality,
            response_format: "url",
            style: "vivid",
          };
          const imagePayload = {
            method: "POST",
            body: JSON.stringify(requestPayload),
            headers: getHeaders(),
          };
          const res = await fetch(imagePath, imagePayload);
          const resJson = await res.json();
          console.log("[Create Image Dalle] ", resJson);
          // check if the response contains an error
          if (resJson.error?.message) {
            throw new Error(resJson.error?.message);
          }
          return {
            prompt,
            url: resJson?.data?.at(0)?.url,
          };
        } catch (e) {
          console.log("[Request] failed to make an image create request", e);
          options.onError?.(e as Error);
          return null;
        }
      }),
    );
    return images?.filter((v) => v !== null);
  }

  /**
   * get image styling advice
   * @param options
   * @param args
   * @returns
   */
  async getImageStylingAdvice(
    options: ChatOptions,
    args: {
      images: Array<string>;
      query: string;
      wardrobe: boolean;
      lang: string;
    },
  ) {
    if (!args?.images?.length && !args?.wardrobe) {
      throw new Error("no images");
    }

    try {
      // get the user's last message
      const lastUserMessageContent = this.getLastUserMessageContent(options);

      // check if the query is empty
      if (!args.query || args.query.replaceAll("\n", "").trim().length === 0) {
        // set the default query to the last user message
        args.query = lastUserMessageContent;
      }
      // user message
      const userMessage: any = {
        role: "user",
        content: [
          {
            type: "text",
            text: args.query,
          },
        ],
      };
      // add wardrobe images
      if (args.wardrobe) {
        const res = await fetch("/api/wardrobe", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });
        const data = await res.json();
        if (data?.data) {
          const textContent = userMessage.content[0];
          for (const img of data.data) {
            if (!img.imageUrl) {
              continue;
            }
            if (img.description) {
              // the image has a description
              textContent.text += `\n${JSON.stringify({
                description: img.description,
                url: img.imageUrl,
              })}`;
            } else {
              // the image has no description, add the image url
              userMessage.content.push({
                type: "image_url",
                image_url: {
                  url: img.imageUrl,
                },
              });
              textContent.text += `\n[(${img.imageUrl}]`;
            }
          }
        }
      }
      if (args.images?.length) {
        // add images from the chat history
        for (const img of args.images) {
          if (!img || !img.includes("blob.vercel-storage")) {
            continue;
          }
          userMessage.content.push({
            type: "image_url",
            image_url: {
              url: img,
            },
          });
        }
      }

      // check if the user message contains images
      const hasImages = userMessage.content.some(
        (v: any) => v.type === "image_url",
      );
      if (!hasImages) {
        // no images, return the text content, don't need to make a request to recognize the description of the image.
        const content = userMessage.content[0].text;
        return content;
      }

      // call the vision model
      // payload
      const body = {
        model: defaultVisionModel,
        messages: [userMessage],
        max_tokens: 1024,
      };
      const payload = {
        method: "POST",
        body: JSON.stringify(body),
        headers: getHeaders(),
      };
      const res = await fetch(this.path(OpenaiPath.ChatPath), payload);
      const resJson = await res.json();
      const content = this.extractMessage(resJson);
      return content;
    } catch (e) {
      console.log("[Request] failed to get image styling advice request", e);
      options.onError?.(e as Error);
      return null;
    }
  }

  /**
   * get the last user message content
   * @param options
   * @returns
   */
  private getLastUserMessageContent(options: ChatOptions) {
    const lastUserMessage = options.messages.findLast((v) => v.role === "user")
      ?.content;
    const lastUserMessageContent =
      typeof lastUserMessage === "string"
        ? lastUserMessage
        : lastUserMessage?.at(0)?.text;
    return lastUserMessageContent || "";
  }

  async usage() {
    const formatDate = (d: Date) =>
      `${d.getFullYear()}-${(d.getMonth() + 1).toString().padStart(2, "0")}-${d
        .getDate()
        .toString()
        .padStart(2, "0")}`;
    const ONE_DAY = 1 * 24 * 60 * 60 * 1000;
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startDate = formatDate(startOfMonth);
    const endDate = formatDate(new Date(Date.now() + ONE_DAY));

    const [used, subs] = await Promise.all([
      fetch(
        this.path(
          `${OpenaiPath.UsagePath}?start_date=${startDate}&end_date=${endDate}`,
        ),
        {
          method: "GET",
          headers: getHeaders(),
        },
      ),
      fetch(this.path(OpenaiPath.SubsPath), {
        method: "GET",
        headers: getHeaders(),
      }),
    ]);

    if (used.status === 401) {
      throw new Error(Locale.Error.Unauthorized);
    }

    if (!used.ok || !subs.ok) {
      throw new Error("Failed to query usage from openai");
    }

    const response = (await used.json()) as {
      total_usage?: number;
      error?: {
        type: string;
        message: string;
      };
    };

    const total = (await subs.json()) as {
      hard_limit_usd?: number;
    };

    if (response.error && response.error.type) {
      throw Error(response.error.message);
    }

    if (response.total_usage) {
      response.total_usage = Math.round(response.total_usage) / 100;
    }

    if (total.hard_limit_usd) {
      total.hard_limit_usd = Math.round(total.hard_limit_usd * 100) / 100;
    }

    return {
      used: response.total_usage,
      total: total.hard_limit_usd,
    } as LLMUsage;
  }

  async models(): Promise<LLMModel[]> {
    if (this.disableListModels) {
      return DEFAULT_MODELS.slice();
    }

    const res = await fetch(this.path(OpenaiPath.ListModelPath), {
      method: "GET",
      headers: {
        ...getHeaders(),
      },
    });

    const resJson = (await res.json()) as OpenAIListModelResponse;
    const chatModels = resJson.data?.filter((m) => m.id.startsWith("gpt-"));
    console.log("[Models]", chatModels);

    if (!chatModels) {
      return [];
    }

    return chatModels.map((m) => ({
      name: m.id,
      available: true,
      provider: {
        id: "stylenow",
        providerName: "Stylenow",
        providerType: "stylenow",
      },
    }));
  }
}

export { OpenaiPath };
