import { ModelProvider, OpenaiPath } from "@/app/constant";
import { NextRequest, NextResponse } from "next/server";
import { auth } from "../../auth";

const ALLOWD_PATH = new Set(Object.values(OpenaiPath));

function getModels() {
  return {
    object: "list",
    data: [
      {
        id: "model-id-0",
        object: "model",
        created: **********,
        owned_by: "organization-owner",
      },
      {
        id: "model-id-1",
        object: "model",
        created: **********,
        owned_by: "organization-owner",
      },
      {
        id: "model-id-2",
        object: "model",
        created: **********,
        owned_by: "openai",
      },
    ],
  };
}

async function handle(
  req: NextRequest,
  { params }: { params: { path: string[] } },
) {
  console.log("[Mock Route] params ", params);

  if (req.method === "OPTIONS") {
    return NextResponse.json({ body: "OK" }, { status: 200 });
  }

  const subpath = params.path.join("/");

  if (!ALLOWD_PATH.has(subpath)) {
    console.log("[Mock Route] forbidden path ", subpath);
    return NextResponse.json(
      {
        error: true,
        msg: "you are not allowed to request " + subpath,
      },
      {
        status: 403,
      },
    );
  }

  const authResult = auth(req, ModelProvider.GPT);
  if (authResult.error) {
    return NextResponse.json(authResult, {
      status: 401,
    });
  }
  try {
    // list models
    if (subpath === OpenaiPath.ListModelPath) {
      const availableModels = getModels();
      return NextResponse.json(availableModels);
    }

    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      start(controller) {
        controller.enqueue(
          encoder.encode(
            `data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo-0613", "system_fingerprint": "fp_44709d6fcb", "choices":[{"index":0,"delta":{"role":"assistant","content":"Hi there, "},"logprobs":null,"finish_reason":null}]}\n\n`,
          ),
        );
        controller.enqueue(
          encoder.encode(
            `data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo-0613", "system_fingerprint": "fp_44709d6fcb", "choices":[{"index":0,"delta":{"content":"this is a test message with an image."},"logprobs":null,"finish_reason":null}]}\n\n`,
          ),
        );
        controller.enqueue(
          encoder.encode(
            `data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo-0613", "system_fingerprint": "fp_44709d6fcb", "choices":[{"index":0,"delta":{"content":"![Allegro](https://y3-wasm.s3.ap-southeast-1.amazonaws.com/allegro.png)"},"logprobs":null,"finish_reason":null}]}\n\n`,
          ),
        );
        controller.enqueue(
          encoder.encode(
            `data: {"id":"chatcmpl-123","object":"chat.completion.chunk","created":**********,"model":"gpt-3.5-turbo-0613", "system_fingerprint": "fp_44709d6fcb", "choices":[{"index":0,"delta":{},"logprobs":null,"finish_reason":"stop"}]}\n\n`,
          ),
        );
        // close the stream
        setTimeout(() => {
          controller.close();
        }, 2000);
      },
    });

    return new NextResponse(stream, {
      headers: {
        "Cache-Control": "no-cache",
        "Content-Type": "text/event-stream",
        Connection: "keep-alive",
      },
    });
  } catch (e) {
    console.error("[Mock] ", e);
    return NextResponse.json(e);
  }
}

export const GET = handle;
export const POST = handle;

export const runtime = "edge";
export const preferredRegion = [
  "arn1",
  "bom1",
  "cdg1",
  "cle1",
  "cpt1",
  "dub1",
  "fra1",
  "gru1",
  "hnd1",
  "iad1",
  "icn1",
  "kix1",
  "lhr1",
  "pdx1",
  "sfo1",
  "sin1",
  "syd1",
];
