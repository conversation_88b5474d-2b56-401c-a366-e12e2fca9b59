import { PrismaClient } from "@prisma/client";
import { NextResponse } from "next/server";
import { createUpstashClient } from "../upstash";
import { VERCEL_STORAGE_URL } from "@/app/constant";
import { uploadImageToS3FromUrl } from "../../image";

const prisma = new PrismaClient();
const regex = /!\[.*?]\((.*?)\)/g;

/**
 * migrate chat history images from vercel blob to S3
 */
export async function POST(req: Request): Promise<NextResponse> {
  // get users
  const users: any[] =
    await prisma.$queryRaw`SELECT "uid" FROM "temp" where "done" = false`;

  let updated = 0;
  for (const user of users) {
    try {
      // get chat history
      const upstashClient = createUpstashClient(user.uid);
      let chatHistory = await upstashClient.get();
      if (!chatHistory) {
        continue;
      }

      // update chat history
      let m;
      let matched = false;
      while ((m = regex.exec(chatHistory)) !== null) {
        if (m.length > 1) {
          const imageUrl = m[1];
          if (imageUrl && imageUrl.includes(VERCEL_STORAGE_URL)) {
            const res = await uploadImageToS3FromUrl(imageUrl);
            if (res.url) {
              chatHistory = chatHistory.replace(imageUrl, res.url);
              matched = true;
            }
          }
        }
      }

      if (matched) {
        await upstashClient.set(user.uid, chatHistory);
        await prisma.$executeRaw`UPDATE "temp" SET "done" = true WHERE "uid" = ${user.uid}`;
        updated++;
      }
    } catch (error) {
      console.error(user.uid, error);
    }
  }
  return NextResponse.json({ total: users.length, updated });
}
