import { createUpstashClient } from "./upstash";
import { NextResponse } from "next/server";

export type UpStashClient = ReturnType<typeof createUpstashClient>;

export async function GET(req: Request): Promise<NextResponse> {
  try {
    const userId = req.headers.get("user_id");
    if (!userId) {
      return NextResponse.json(
        { error: "user_id is empty", data: [] },
        { status: 400 },
      );
    }
    // Get data from upstash by user id
    const upstashClient = createUpstashClient(userId);
    const data = await upstashClient.get();
    return NextResponse.json({ data });
  } catch (error) {
    return NextResponse.json(
      { error: (error as Error).message, data: [] },
      { status: 400 },
    );
  }
}

export async function POST(req: Request): Promise<NextResponse> {
  try {
    const body = (await req.json()) as {
      data: string;
    };
    if (!body) {
      return NextResponse.json({ error: "body is empty" }, { status: 400 });
    }
    if (!body.data) {
      return NextResponse.json({ error: "data is empty" }, { status: 400 });
    }
    const userId = req.headers.get("user_id");
    if (!userId) {
      return NextResponse.json({ error: "user_id is empty" }, { status: 400 });
    }
    const upstashClient = createUpstashClient(userId);
    await upstashClient.set(userId, body.data);
    return NextResponse.json({ data: "success" });
  } catch (error) {
    return NextResponse.json(
      { error: (error as Error).message, data: [] },
      { status: 400 },
    );
  }
}
