import { type OpenAIListModelResponse } from "@/app/client/platforms/openai";
import { getServerSideConfig } from "@/app/config/server";
import { ModelProvider, OpenaiPath } from "@/app/constant";
import { NextRequest, NextResponse } from "next/server";
import { auth } from "../../auth";
import { requestOpenai } from "../../common";
import { OpenAICreateImageResponse } from "@/app/client/platforms/stylenow";
import { uploadImageToS3 } from "../../image";

const ALLOWD_PATH = new Set(Object.values(OpenaiPath));

function getModels(remoteModelRes: OpenAIListModelResponse) {
  const config = getServerSideConfig();

  if (config.disableGPT4) {
    remoteModelRes.data = remoteModelRes.data.filter(
      (m) => !m.id.startsWith("gpt-4"),
    );
  }

  return remoteModelRes;
}

async function handle(
  req: NextRequest,
  { params }: { params: { path: string[] } },
) {
  console.log("[OpenAI Route] params ", params);

  if (req.method === "OPTIONS") {
    return NextResponse.json({ body: "OK" }, { status: 200 });
  }

  const subpath = params.path.join("/");

  if (!ALLOWD_PATH.has(subpath)) {
    console.log("[OpenAI Route] forbidden path ", subpath);
    return NextResponse.json(
      {
        error: true,
        msg: "you are not allowed to request " + subpath,
      },
      {
        status: 403,
      },
    );
  }

  const authResult = auth(req, ModelProvider.GPT);
  if (authResult.error) {
    return NextResponse.json(authResult, {
      status: 401,
    });
  }

  try {
    let nonSystemPrompt = true;
    // check url query param to see if we should add system prompt
    if (req.nextUrl.searchParams.get("systemPrompt") === "true") {
      nonSystemPrompt = false;
    }
    const response = await requestOpenai(req, nonSystemPrompt);

    // list models
    if (subpath === OpenaiPath.ListModelPath && response.status === 200) {
      const resJson = (await response.json()) as OpenAIListModelResponse;
      const availableModels = getModels(resJson);
      return NextResponse.json(availableModels, {
        status: response.status,
      });
    }
    // images generation
    if (subpath === OpenaiPath.CreateImage && response.status === 200) {
      const resJson = (await response.json()) as OpenAICreateImageResponse;
      await uploadImagesToVercel(resJson);
      return NextResponse.json(resJson, {
        status: response.status,
      });
    }

    return response;
  } catch (e) {
    console.error("[OpenAI] ", e);
    return NextResponse.json(e);
  }
}

export const GET = handle;
export const POST = handle;

export const runtime = "edge";
export const preferredRegion = [
  "arn1",
  "bom1",
  "cdg1",
  "cle1",
  "cpt1",
  "dub1",
  "fra1",
  "gru1",
  "hnd1",
  "iad1",
  "icn1",
  "kix1",
  "lhr1",
  "pdx1",
  "sfo1",
  "sin1",
  "syd1",
];

/**
 * upload images to vercel
 * @param res
 * @returns
 */
async function uploadImagesToVercel(res: OpenAICreateImageResponse) {
  const images = res.data;
  if (!images) {
    return;
  }
  for (const img of images) {
    try {
      if (img.url) {
        // get file name
        const urlObject = new URL(img.url);
        const pathname = urlObject.pathname;
        const ext = pathname.split(".").pop();
        const filename = `${Date.now()}.${ext || "png"}`;
        // download image
        const res = await fetch(img.url);
        const data = await res.blob();
        const blob = await uploadImageToS3(new File([data], filename));
        console.log("[Upload Image To S3] ", blob);
        // replace the url with the S3 url
        img.url = blob.url;
      } else if (img.b64_json) {
        const json = JSON.parse(atob(img.b64_json));
        for (const key in json) {
          const data = json[key];
          const ext = key.split(".").pop();
          const filename = `${Date.now()}.${ext || "png"}`;
          const blob = await uploadImageToS3(new File([data], filename));
          console.log("[Upload Image To Vercel] ", blob);
          // replace the url with the vercel url
          img.url = blob.url;
          break;
        }
      }
    } catch (e) {
      console.log("[Upload Image To Vercel] failed to upload image", img, e);
    }
  }
}
