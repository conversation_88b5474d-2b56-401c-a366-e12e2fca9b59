import { NextResponse, NextRequest } from "next/server";
import { deleteWardrobeAll } from "../wardrobe";

/**
 * delete user
 */
export async function DELETE(req: Request): Promise<NextResponse> {
  try {
    const userId = req.headers.get("user_id");
    if (!userId) {
      return NextResponse.json({ error: "user_id is empty" }, { status: 400 });
    }

    // delete wardrobe by userId
    await deleteWardrobe<PERSON>ll(userId);
    return NextResponse.json({ status: 200 });
  } catch (error) {
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 400 },
    );
  }
}
