import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { nanoid } from "nanoid";

const prisma = new PrismaClient();

export async function GET(req: NextRequest, res: NextResponse) {
  const shareId = req.nextUrl.searchParams.get("share_id");

  if (!shareId) {
    return NextResponse.json("share_id is required", { status: 400 });
  }

  try {
    const dialogue = await prisma.dialogue.findUnique({
      where: { id: shareId },
    });

    if (!dialogue) {
      return NextResponse.json("share_id not found", { status: 404 });
    }
    return NextResponse.json(dialogue.message, { status: 200 });
  } catch (e) {
    return new NextResponse((e as Error)?.message, { status: 500 });
  }
}

export async function POST(req: NextRequest, res: NextResponse) {
  const message = await req.json();

  try {
    const shareId = nanoid();
    await prisma.dialogue.create({
      data: {
        id: shareId,
        message,
        userId: req.headers.get("user_id"),
      },
    });
    return new NextResponse(`/share/${shareId}`, { status: 200 });
  } catch (e) {
    return new NextResponse((e as Error)?.message, { status: 500 });
  }
}
