import { PrismaClient } from "@prisma/client";
import { NextResponse } from "next/server";
import { uploadImageToS3FromUrl } from "../../image";
import { VERCEL_STORAGE_URL } from "@/app/constant";

const prisma = new PrismaClient();
const regex = /!\[.*?]\((.*?)\)/g;

/**
 * migrate dialogue images from vercel blob to S3
 * @param req
 * @returns
 */
export async function POST(req: Request): Promise<NextResponse> {
  // get all vercel blob images, save them to S3, then update the Dialogue table
  const dialogueList = await prisma.dialogue.findMany({
    orderBy: {
      createdAt: "asc",
    },
  });

  let updated = 0;
  for (const dialogue of dialogueList) {
    if (!Array.isArray(dialogue.message) || dialogue.message === null) {
      continue;
    }
    const messages: any[] = dialogue.message;
    try {
      let matched = false;
      for (const message of messages) {
        if (typeof message !== "object" || !message) {
          continue;
        }
        if (typeof message.content === "string") {
          // extract image url from message content, upload to S3 and replace the content.
          let m;
          while ((m = regex.exec(message.content)) !== null) {
            if (m.length > 1) {
              const imageUrl = m[1];
              if (imageUrl && imageUrl.includes(VERCEL_STORAGE_URL)) {
                const res = await uploadImageToS3FromUrl(imageUrl);
                if (res.url) {
                  message.content = message.content.replace(imageUrl, res.url);
                  matched = true;
                }
              }
            }
          }
        } else if (Array.isArray(message.content)) {
          // check if the content is an array of objects
          for (const content of message.content) {
            if (typeof content === "object" && content) {
              if (
                content.image_url &&
                typeof content.image_url === "object" &&
                content.image_url.url
              ) {
                // extract image url from message content, upload to S3 and replace the content.
                const imageUrl = content.image_url.url;
                if (imageUrl.includes(VERCEL_STORAGE_URL)) {
                  const res = await uploadImageToS3FromUrl(imageUrl);
                  if (res.url) {
                    content.image_url.url = res.url;
                    matched = true;
                  }
                }
              }
            }
          }
        }
      }
      if (matched) {
        await prisma.dialogue.update({
          where: {
            id: dialogue.id,
          },
          data: {
            message: messages,
          },
        });
        updated++;
      }
    } catch (error) {
      console.error(dialogue.id, error);
    }
  }

  return NextResponse.json({ total: dialogueList.length, updated });
}
