import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

/**
 * wardrobe image
 */
export interface Wardrobe {
  id: string;
  userId: string;
  imageName: string;
  imageUrl: string;
  downloadUrl: string;
  description: string | null;
  createdAt?: Date;
}

/**
 * get wardrobe list by user id
 * @param userId user id
 * @returns
 */
export async function getWardrobeList(userId: string): Promise<Wardrobe[]> {
  return await prisma.wardrobe.findMany({
    where: {
      userId: userId,
      deleted: false,
    },
    orderBy: {
      createdAt: "desc",
    },
  });
}

/**
 * create wardrobe
 * @param wardrobe
 * @returns
 */
export async function createWardrobe(wardrobe: Wardrobe): Promise<Wardrobe> {
  const data = await prisma.wardrobe.create({
    data: wardrobe,
  });
  // add image description
  if (data.id && data.imageUrl) {
    await addImageDescription(data.id);
  }

  return data;
}

/**
 * delete wardrobe
 * @param id
 * @returns
 */
export async function deleteWardrobe(
  id: string,
  userId: string,
): Promise<Wardrobe> {
  return await prisma.wardrobe.update({
    where: {
      id: id,
      userId: userId,
    },
    data: {
      deleted: true,
    },
  });
}

/**
 * delete wardrobe
 * @param userId
 * @returns
 */
export async function deleteWardrobeAll(
  userId: string,
): Promise<{ count: number }> {
  return await prisma.wardrobe.updateMany({
    where: {
      userId: userId,
    },
    data: {
      deleted: true,
    },
  });
}

/**
 * enrich description to wardrobe
 */
export async function enrichDescription(take = 2) {
  const wardrobeList = await prisma.wardrobe.findMany({
    where: {
      description: "",
      deleted: false,
    },
    take: take,
  });
  for (const wardrobe of wardrobeList) {
    if (wardrobe.id && wardrobe.imageUrl) {
      await addImageDescription(wardrobe.id);
    }
  }
  return wardrobeList;
}

/**
 * add image description
 * @param id image id
 * @param imageUrl image url
 * @returns
 */
// async function addImageDescription(id: string, imageUrl: string) {
//   try {
//     const systemMessage = {
//       role: "system",
//       content: [
//         {
//           type: "text",
//           text: DEFAULT_VISION_TEMPLATE,
//         },
//       ],
//     };
//     const userMessage = {
//       role: "user",
//       content: [
//         {
//           type: "text",
//           text: "What's the detail in this image? And which weather, seaon and situation is it suitable for?",
//         },
//         {
//           type: "image_url",
//           image_url: imageUrl,
//         },
//       ],
//     };
//     const body = {
//       model: "gpt-4-vision-preview",
//       messages: [systemMessage, userMessage],
//       max_tokens: 512,
//     };
//     const payload = {
//       method: "POST",
//       body: JSON.stringify(body),
//       headers: {
//         "Content-Type": "application/json",
//         Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
//       },
//     };
//     const res = await fetch(OPENAI_BASE_URL + OpenaiPath.ChatPath, payload);
//     const resJson = await res.json();
//     const description = resJson?.choices?.at(0)?.message?.content ?? "";
//     if (description) {
//       await prisma.wardrobe.update({
//         where: {
//           id: id,
//         },
//         data: {
//           description: description,
//         },
//       });
//     }
//   } catch (e) {
//     console.log("[Request] failed to get image description request", e);
//   }
// }

async function addImageDescription(id: string) {
  const payload = {
    method: "POST",
    body: JSON.stringify({
      messages: [
        {
          content: `begin to describe the image that id is "${id}"`,
          role: "user",
        },
      ],
    }),
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
    },
  };
  await fetch(<string>process.env.BASE_URL, payload);
}

export async function getWardrobeByImageUrl(url: string, userId: string) {
  const response = await prisma.wardrobe.findFirst({
    where: {
      userId,
      imageUrl: url,
      deleted: false,
    },
  });

  return response;
}
