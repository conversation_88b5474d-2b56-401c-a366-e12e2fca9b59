import {
  S3Client,
  GetObjectCommand,
  PutObjectCommand,
} from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import { nanoid } from "nanoid";
import { Readable } from "stream";

const bucket = process.env.AWS_BUCKET_NAME;
const region = process.env.AWS_REGION;
const s3 = new S3Client({
  region,
  credentials: {
    accessKeyId: process.env.AWS_KEY_ID as string,
    secretAccessKey: process.env.AWS_ACCESS_SECRET_KEY as string,
  },
});

/**
 * upload image file to S3
 */
export async function uploadImageToS3(image: File): Promise<{
  pathname: string;
  url: string;
  downloadUrl: string;
  contentType: string;
  contentDisposition: string;
}> {
  let imageName = encodeURIComponent(image.name);
  // add nanoid behind the image name
  // format: [image name]-[nanoid].[ext]
  const ext = imageName.split(".").pop();
  if (ext) {
    imageName = `${imageName
      .split(".")
      .slice(0, -1)
      .join(".")}-${nanoid()}.${ext}`;
  } else {
    imageName = `${imageName}-${nanoid()}`;
  }
  // upload image to S3
  const body = (await image.arrayBuffer()) as Buffer;
  await s3.send(
    new PutObjectCommand({
      ACL: "public-read",
      Bucket: bucket,
      Key: imageName,
      Body: body,
    }),
  );
  const url = `https://${bucket}.s3.${region}.amazonaws.com/${imageName}`;
  return {
    url,
    downloadUrl: url,
    pathname: imageName,
    contentType: image.type,
    contentDisposition: `inline; filename="${image.name}"`,
  };
}

/**
 * upload image file to S3 from URL
 * @param url
 * @returns
 */
export async function uploadImageToS3FromUrl(url: string): Promise<{
  pathname: string;
  url: string;
  downloadUrl: string;
  contentType: string;
  contentDisposition: string;
}> {
  const imageName = encodeURIComponent(url.split("/").pop() || "");
  if (!imageName) {
    throw new Error("Invalid image url");
  }
  // download image from url
  const body = (await fetch(url)).body;
  if (!body) {
    throw new Error("Image not found");
  }
  // upload image to S3
  const res = await new Upload({
    client: s3,
    params: {
      ACL: "public-read",
      Bucket: bucket,
      Key: imageName,
      Body: body,
    },
  });
  await res.done();
  const imageUrl = `https://${bucket}.s3.${region}.amazonaws.com/${imageName}`;
  return {
    url: imageUrl,
    downloadUrl: imageUrl,
    pathname: imageName,
    contentType: "image",
    contentDisposition: `inline; filename="${imageName}"`,
  };
}

/**
 * download image file from S3
 */
export async function downloadImageFromS3(url: string): Promise<Readable> {
  const imageName = url.split("/").pop();
  if (!imageName) {
    throw new Error("Invalid image url");
  }
  const { Body } = await s3.send(
    new GetObjectCommand({
      Bucket: bucket,
      Key: imageName,
    }),
  );
  if (!Body) {
    throw new Error("Image not found");
  }
  return Body as Readable;
}
