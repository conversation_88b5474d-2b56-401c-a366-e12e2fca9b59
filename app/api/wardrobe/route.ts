import { NextResponse, NextRequest } from "next/server";
import {
  create<PERSON><PERSON><PERSON>,
  delete<PERSON><PERSON><PERSON>,
  getWardrobe<PERSON><PERSON>,
  getWardrobeByImageUrl,
} from "../wardrobe";
import { nanoid } from "nanoid";

/**
 * get wardrobe list by current user id
 * @param req
 * @returns
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  const userId = req.headers.get("user_id");
  if (!userId) {
    return NextResponse.json(
      { error: "user_id is empty", data: [] },
      { status: 400 },
    );
  }

  const { searchParams } = new URL(req.url);
  const imageUrl = searchParams.get("imageUrl");
  if (imageUrl) {
    try {
      const response = await getWardrobeByImageUrl(imageUrl, userId);
      return NextResponse.json({ data: response });
    } catch (error) {
      return NextResponse.json(
        { error: (error as Error).message, data: [] },
        { status: 400 },
      );
    }
  }

  try {
    // Get wardrobe list by user id
    const wardrobeList = await getW<PERSON>robeList(userId);
    return NextResponse.json({ data: wardrobeList });
  } catch (error) {
    return NextResponse.json(
      { error: (error as Error).message, data: [] },
      { status: 400 },
    );
  }
}

/**
 * upload image to wardrobe
 * @param req
 * @returns
 */
export async function POST(req: Request): Promise<NextResponse> {
  try {
    const body = (await req.json()) as {
      imageName: string;
      imageUrl: string;
      downloadUrl: string;
    };
    if (!body) {
      return NextResponse.json({ error: "body is empty" }, { status: 400 });
    }
    if (!body.imageName) {
      return NextResponse.json(
        { error: "imageName is empty" },
        { status: 400 },
      );
    }
    if (!body.imageUrl) {
      return NextResponse.json({ error: "imageUrl is empty" }, { status: 400 });
    }
    const userId = req.headers.get("user_id");
    if (!userId) {
      return NextResponse.json({ error: "user_id is empty" }, { status: 400 });
    }
    const result = await createWardrobe({
      id: nanoid(),
      userId: userId,
      imageName: body.imageName,
      imageUrl: body.imageUrl,
      downloadUrl: body.downloadUrl,
      description: null,
    });
    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 400 }, // The webhook will retry 5 times waiting for a 200
    );
  }
}

/**
 * delete wardrobe by id
 */
export async function DELETE(req: Request): Promise<NextResponse> {
  try {
    const userId = req.headers.get("user_id");
    if (!userId) {
      return NextResponse.json({ error: "user_id is empty" }, { status: 400 });
    }
    // get id from body
    const params = await req.json();
    if (!params?.id) {
      return NextResponse.json({ error: "id is empty" }, { status: 400 });
    }
    // delete wardrobe by id
    const wardrobe = await deleteWardrobe(params.id, userId);
    return NextResponse.json(wardrobe);
  } catch (error) {
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 400 },
    );
  }
}
