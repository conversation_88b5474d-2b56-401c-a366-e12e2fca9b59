import { NextRequest, NextResponse } from "next/server";
import { enrichDescription } from "../../wardrobe";

async function handler(req: NextRequest) {
  const response = await update();
  return new NextResponse(JSON.stringify(response), {
    status: 200,
  });
}

/**
 * update the description of wardrobe images.
 * @returns
 */
async function update() {
  const response = await enrichDescription();

  return response;
}

export const GET = handler;
export const POST = handler;

export const maxDuration = 60;
