import { PrismaClient } from "@prisma/client";
import { NextResponse } from "next/server";
import { uploadImageToS3FromUrl } from "../../image";

const prisma = new PrismaClient();

/**
 * migrate wardrobe images from vercel blob to S3
 * @param req
 * @returns
 */
export async function POST(req: Request): Promise<NextResponse> {
  // get all vercel blob images, save them to S3, then update the wardrobe table
  const wardrobeList = await prisma.wardrobe.findMany({
    where: {
      imageUrl: {
        contains: "vercel-storage.com",
      },
      deleted: false,
    },
    orderBy: {
      createdAt: "asc",
    },
  });

  let updated = 0;
  for (const wardrobe of wardrobeList) {
    try {
      const res = await uploadImageToS3FromUrl(wardrobe.imageUrl);
      await prisma.wardrobe.update({
        where: {
          id: wardrobe.id,
        },
        data: {
          imageUrl: res.url,
          downloadUrl: res.downloadUrl,
        },
      });
      updated++;
    } catch (error) {
      console.error(wardrobe.imageUrl, error);
    }
  }

  return NextResponse.json({ total: wardrobeList.length, updated });
}
