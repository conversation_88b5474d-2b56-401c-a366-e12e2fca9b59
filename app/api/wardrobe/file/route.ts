import { NextResponse } from "next/server";
import { createWardrobe } from "../../wardrobe";
import { nanoid } from "nanoid";
import { uploadImageToS3 } from "../../image";

/**
 * upload image file to wardrobe
 * @param req
 * @returns
 */
export async function POST(req: Request): Promise<NextResponse> {
  try {
    const formData = await req.formData();
    if (!formData) {
      return NextResponse.json({ error: "body is empty" }, { status: 400 });
    }

    const image = formData.get("image") as File;
    if (!image) {
      return NextResponse.json({ error: "image is empty" }, { status: 400 });
    }

    // check if the file is an image
    if (!image.type.startsWith("image/")) {
      return NextResponse.json(
        { error: "file is not an image" },
        { status: 400 },
      );
    }

    // get user id from headers
    const userId = req.headers.get("user_id");
    if (!userId) {
      return NextResponse.json({ error: "user_id is empty" }, { status: 400 });
    }

    // upload image to S3
    const res = await uploadImageToS3(image);

    // save wardrobe image to database
    const result = await createWardrobe({
      id: nanoid(),
      userId: userId,
      imageName: image.name,
      imageUrl: res.url,
      downloadUrl: res.downloadUrl,
      description: null,
    });
    return NextResponse.json(result);
  } catch (error) {
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 400 }, // The webhook will retry 5 times waiting for a 200
    );
  }
}
